"use strict";var Ku=Object.create;var to=Object.defineProperty;var zu=Object.getOwnPropertyDescriptor;var Xu=Object.getOwnPropertyNames;var Yu=Object.getPrototypeOf,qu=Object.prototype.hasOwnProperty;var Ju=(e,n,t)=>n in e?to(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var Qu=(e,n)=>{for(var t in n)to(e,t,{get:n[t],enumerable:!0})},ms=(e,n,t,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of Xu(n))!qu.call(e,r)&&r!==t&&to(e,r,{get:()=>n[r],enumerable:!(o=zu(n,r))||o.enumerable});return e};var le=(e,n,t)=>(t=e!=null?Ku(Yu(e)):{},ms(n||!e||!e.__esModule?to(t,"default",{value:e,enumerable:!0}):t,e)),Zu=e=>ms(to({},"__esModule",{value:!0}),e);var Oe=(e,n,t)=>(Ju(e,typeof n!="symbol"?n+"":n,t),t),Ts=(e,n,t)=>{if(!n.has(e))throw TypeError("Cannot "+t)};var Xe=(e,n,t)=>(Ts(e,n,"read from private field"),t?t.call(e):n.get(e)),Ho=(e,n,t)=>{if(n.has(e))throw TypeError("Cannot add the same private member more than once");n instanceof WeakSet?n.add(e):n.set(e,t)},Br=(e,n,t,o)=>(Ts(e,n,"write to private field"),o?o.call(e,t):n.set(e,t),t);var Wm={};Qu(Wm,{Button:()=>li,Checkbox:()=>Mc,CloseButton:()=>wc,Combobox:()=>pd,ComboboxButton:()=>Oa,ComboboxInput:()=>Da,ComboboxLabel:()=>La,ComboboxOption:()=>Ma,ComboboxOptions:()=>Ia,DataInteractive:()=>bd,Description:()=>Lt,Dialog:()=>Wd,DialogBackdrop:()=>Ud,DialogDescription:()=>Vd,DialogPanel:()=>Ka,DialogTitle:()=>za,Disclosure:()=>np,DisclosureButton:()=>Qa,DisclosurePanel:()=>Za,Field:()=>ip,Fieldset:()=>lp,FocusTrap:()=>Vi,FocusTrapFeatures:()=>Cr,Input:()=>cp,Label:()=>nt,Legend:()=>dp,Listbox:()=>Cp,ListboxButton:()=>cu,ListboxLabel:()=>fu,ListboxOption:()=>pu,ListboxOptions:()=>du,ListboxSelectedOption:()=>mu,Menu:()=>Wp,MenuButton:()=>bu,MenuHeading:()=>vu,MenuItem:()=>yu,MenuItems:()=>gu,MenuSection:()=>hu,MenuSeparator:()=>Eu,Popover:()=>im,PopoverBackdrop:()=>Au,PopoverButton:()=>Su,PopoverGroup:()=>Du,PopoverOverlay:()=>Cu,PopoverPanel:()=>Ou,Portal:()=>lt,Radio:()=>Iu,RadioGroup:()=>Tm,RadioGroupDescription:()=>Fu,RadioGroupLabel:()=>Mu,RadioGroupOption:()=>Lu,Select:()=>ym,Switch:()=>Rm,SwitchDescription:()=>ku,SwitchGroup:()=>_u,SwitchLabel:()=>$u,Tab:()=>Bm,TabGroup:()=>Gu,TabList:()=>Uu,TabPanel:()=>Wu,TabPanels:()=>Vu,Textarea:()=>Vm,Transition:()=>ji,TransitionChild:()=>wo,useClose:()=>nr});module.exports=Zu(Wm);var bs=le(require("react"),1),no=typeof document!="undefined"?bs.default.useLayoutEffect:()=>{};var No=require("react");function Gr(e){let n=(0,No.useRef)(null);return no(()=>{n.current=e},[e]),(0,No.useCallback)((...t)=>{let o=n.current;return o==null?void 0:o(...t)},[])}var Ye=e=>{var n;return(n=e==null?void 0:e.ownerDocument)!==null&&n!==void 0?n:document},mt=e=>e&&"window"in e&&e.window===e?e:Ye(e).defaultView||window;function ec(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function Ur(e){return ec(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}var tc=!1;function Bo(){return tc}function oo(e,n){if(!Bo())return n&&e?e.contains(n):!1;if(!e||!n)return!1;let t=n;for(;t!==null;){if(t===e)return!0;t.tagName==="SLOT"&&t.assignedSlot?t=t.assignedSlot.parentNode:Ur(t)?t=t.host:t=t.parentNode}return!1}var yn=(e=document)=>{var n;if(!Bo())return e.activeElement;let t=e.activeElement;for(;t&&"shadowRoot"in t&&(!((n=t.shadowRoot)===null||n===void 0)&&n.activeElement);)t=t.shadowRoot.activeElement;return t};function ro(e){return Bo()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}function Go(e){var n;return typeof window=="undefined"||window.navigator==null?!1:((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent)}function Vr(e){var n;return typeof window!="undefined"&&window.navigator!=null?e.test(((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.platform)||window.navigator.platform):!1}function At(e){let n=null;return()=>(n==null&&(n=e()),n)}var io=At(function(){return Vr(/^Mac/i)}),gs=At(function(){return Vr(/^iPhone/i)}),ys=At(function(){return Vr(/^iPad/i)||io()&&navigator.maxTouchPoints>1}),hs=At(function(){return gs()||ys()}),nc=At(function(){return io()||hs()}),oc=At(function(){return Go(/AppleWebKit/i)&&!vs()}),vs=At(function(){return Go(/Chrome/i)}),Wr=At(function(){return Go(/Android/i)}),rc=At(function(){return Go(/Firefox/i)});var Bt=require("react");function so(){let e=(0,Bt.useRef)(new Map),n=(0,Bt.useCallback)((r,i,s,l)=>{let a=l!=null&&l.once?(...c)=>{e.current.delete(s),s(...c)}:s;e.current.set(s,{type:i,eventTarget:r,fn:a,options:l}),r.addEventListener(i,a,l)},[]),t=(0,Bt.useCallback)((r,i,s,l)=>{var a;let c=((a=e.current.get(s))===null||a===void 0?void 0:a.fn)||s;r.removeEventListener(i,c,l),e.current.delete(s)},[]),o=(0,Bt.useCallback)(()=>{e.current.forEach((r,i)=>{t(r.eventTarget,r.type,i,r.options)})},[t]);return(0,Bt.useEffect)(()=>o,[o]),{addGlobalListener:n,removeGlobalListener:t,removeAllGlobalListeners:o}}function jr(e){return e.mozInputSource===0&&e.isTrusted?!0:Wr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}var Uo=require("react");function Kr(e){let n=e;return n.nativeEvent=e,n.isDefaultPrevented=()=>n.defaultPrevented,n.isPropagationStopped=()=>n.cancelBubble,n.persist=()=>{},n}function Es(e,n){Object.defineProperty(e,"target",{value:n}),Object.defineProperty(e,"currentTarget",{value:n})}function Vo(e){let n=(0,Uo.useRef)({isFocused:!1,observer:null});no(()=>{let o=n.current;return()=>{o.observer&&(o.observer.disconnect(),o.observer=null)}},[]);let t=Gr(o=>{e==null||e(o)});return(0,Uo.useCallback)(o=>{if(o.target instanceof HTMLButtonElement||o.target instanceof HTMLInputElement||o.target instanceof HTMLTextAreaElement||o.target instanceof HTMLSelectElement){n.current.isFocused=!0;let r=o.target,i=s=>{if(n.current.isFocused=!1,r.disabled){let l=Kr(s);t(l)}n.current.observer&&(n.current.observer.disconnect(),n.current.observer=null)};r.addEventListener("focusout",i,{once:!0}),n.current.observer=new MutationObserver(()=>{if(n.current.isFocused&&r.disabled){var s;(s=n.current.observer)===null||s===void 0||s.disconnect();let l=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),n.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[t])}var zr=!1;var Jr=require("react");var ao=null,Xr=new Set,lo=new Map,nn=!1,Yr=!1,ic={Tab:!0,Escape:!0};function Qr(e,n){for(let t of Xr)t(e,n)}function sc(e){return!(e.metaKey||!io()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Wo(e){nn=!0,sc(e)&&(ao="keyboard",Qr("keyboard",e))}function hn(e){ao="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(nn=!0,Qr("pointer",e))}function xs(e){jr(e)&&(nn=!0,ao="virtual")}function Ps(e){e.target===window||e.target===document||zr||!e.isTrusted||(!nn&&!Yr&&(ao="virtual",Qr("virtual",e)),nn=!1,Yr=!1)}function Rs(){zr||(nn=!1,Yr=!0)}function qr(e){if(typeof window=="undefined"||lo.get(mt(e)))return;let n=mt(e),t=Ye(e),o=n.HTMLElement.prototype.focus;n.HTMLElement.prototype.focus=function(){nn=!0,o.apply(this,arguments)},t.addEventListener("keydown",Wo,!0),t.addEventListener("keyup",Wo,!0),t.addEventListener("click",xs,!0),n.addEventListener("focus",Ps,!0),n.addEventListener("blur",Rs,!1),typeof PointerEvent!="undefined"&&(t.addEventListener("pointerdown",hn,!0),t.addEventListener("pointermove",hn,!0),t.addEventListener("pointerup",hn,!0)),n.addEventListener("beforeunload",()=>{Ss(e)},{once:!0}),lo.set(n,{focus:o})}var Ss=(e,n)=>{let t=mt(e),o=Ye(e);n&&o.removeEventListener("DOMContentLoaded",n),lo.has(t)&&(t.HTMLElement.prototype.focus=lo.get(t).focus,o.removeEventListener("keydown",Wo,!0),o.removeEventListener("keyup",Wo,!0),o.removeEventListener("click",xs,!0),t.removeEventListener("focus",Ps,!0),t.removeEventListener("blur",Rs,!1),typeof PointerEvent!="undefined"&&(o.removeEventListener("pointerdown",hn,!0),o.removeEventListener("pointermove",hn,!0),o.removeEventListener("pointerup",hn,!0)),lo.delete(t))};function Cs(e){let n=Ye(e),t;return n.readyState!=="loading"?qr(e):(t=()=>{qr(e)},n.addEventListener("DOMContentLoaded",t)),()=>Ss(e,t)}typeof document!="undefined"&&Cs();function jo(){return ao!=="pointer"}var lc=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ac(e,n,t){let o=Ye(t==null?void 0:t.target),r=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLInputElement:HTMLInputElement,i=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLTextAreaElement:HTMLTextAreaElement,s=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLElement:HTMLElement,l=typeof window!="undefined"?mt(t==null?void 0:t.target).KeyboardEvent:KeyboardEvent;return e=e||o.activeElement instanceof r&&!lc.has(o.activeElement.type)||o.activeElement instanceof i||o.activeElement instanceof s&&o.activeElement.isContentEditable,!(e&&n==="keyboard"&&t instanceof l&&!ic[t.key])}function Zr(e,n,t){qr(),(0,Jr.useEffect)(()=>{let o=(r,i)=>{ac(!!(t!=null&&t.isTextInput),r,i)&&e(jo())};return Xr.add(o),()=>{Xr.delete(o)}},n)}var ei=require("react");function ti(e){let{isDisabled:n,onFocus:t,onBlur:o,onFocusChange:r}=e,i=(0,ei.useCallback)(a=>{if(a.target===a.currentTarget)return o&&o(a),r&&r(!1),!0},[o,r]),s=Vo(i),l=(0,ei.useCallback)(a=>{let c=Ye(a.target),u=c?yn(c):yn();a.target===a.currentTarget&&u===ro(a.nativeEvent)&&(t&&t(a),r&&r(!0),s(a))},[r,t,s]);return{focusProps:{onFocus:!n&&(t||r||o)?l:void 0,onBlur:!n&&(o||r)?i:void 0}}}var uo=require("react");function ni(e){let{isDisabled:n,onBlurWithin:t,onFocusWithin:o,onFocusWithinChange:r}=e,i=(0,uo.useRef)({isFocusWithin:!1}),{addGlobalListener:s,removeAllGlobalListeners:l}=so(),a=(0,uo.useCallback)(p=>{p.currentTarget.contains(p.target)&&i.current.isFocusWithin&&!p.currentTarget.contains(p.relatedTarget)&&(i.current.isFocusWithin=!1,l(),t&&t(p),r&&r(!1))},[t,r,i,l]),c=Vo(a),u=(0,uo.useCallback)(p=>{if(!p.currentTarget.contains(p.target))return;let d=Ye(p.target),f=yn(d);if(!i.current.isFocusWithin&&f===ro(p.nativeEvent)){o&&o(p),r&&r(!0),i.current.isFocusWithin=!0,c(p);let m=p.currentTarget;s(d,"focus",T=>{if(i.current.isFocusWithin&&!oo(m,T.target)){let b=new d.defaultView.FocusEvent("blur",{relatedTarget:T.target});Es(b,m);let g=Kr(b);a(g)}},{capture:!0})}},[o,r,c,s,a]);return n?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:a}}}var Ot=require("react"),ri=!1,oi=0;function uc(){ri=!0,setTimeout(()=>{ri=!1},50)}function As(e){e.pointerType==="touch"&&uc()}function cc(){if(typeof document!="undefined")return typeof PointerEvent!="undefined"&&document.addEventListener("pointerup",As),oi++,()=>{oi--,!(oi>0)&&typeof PointerEvent!="undefined"&&document.removeEventListener("pointerup",As)}}function fe(e){let{onHoverStart:n,onHoverChange:t,onHoverEnd:o,isDisabled:r}=e,[i,s]=(0,Ot.useState)(!1),l=(0,Ot.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,Ot.useEffect)(cc,[]);let{addGlobalListener:a,removeAllGlobalListeners:c}=so(),{hoverProps:u,triggerHoverEnd:p}=(0,Ot.useMemo)(()=>{let d=(T,b)=>{if(l.pointerType=b,r||b==="touch"||l.isHovered||!T.currentTarget.contains(T.target))return;l.isHovered=!0;let g=T.currentTarget;l.target=g,a(Ye(T.target),"pointerover",h=>{l.isHovered&&l.target&&!oo(l.target,h.target)&&f(h,h.pointerType)},{capture:!0}),n&&n({type:"hoverstart",target:g,pointerType:b}),t&&t(!0),s(!0)},f=(T,b)=>{let g=l.target;l.pointerType="",l.target=null,!(b==="touch"||!l.isHovered||!g)&&(l.isHovered=!1,c(),o&&o({type:"hoverend",target:g,pointerType:b}),t&&t(!1),s(!1))},m={};return typeof PointerEvent!="undefined"&&(m.onPointerEnter=T=>{ri&&T.pointerType==="mouse"||d(T,T.pointerType)},m.onPointerLeave=T=>{!r&&T.currentTarget.contains(T.target)&&f(T,T.pointerType)}),{hoverProps:m,triggerHoverEnd:f}},[n,t,o,r,l,a,c]);return(0,Ot.useEffect)(()=>{r&&p({currentTarget:l.target},l.pointerType)},[r]),{hoverProps:u,isHovered:i}}var Gt=require("react");function ce(e={}){let{autoFocus:n=!1,isTextInput:t,within:o}=e,r=(0,Gt.useRef)({isFocused:!1,isFocusVisible:n||jo()}),[i,s]=(0,Gt.useState)(!1),[l,a]=(0,Gt.useState)(()=>r.current.isFocused&&r.current.isFocusVisible),c=(0,Gt.useCallback)(()=>a(r.current.isFocused&&r.current.isFocusVisible),[]),u=(0,Gt.useCallback)(f=>{r.current.isFocused=f,s(f),c()},[c]);Zr(f=>{r.current.isFocusVisible=f,c()},[],{isTextInput:t});let{focusProps:p}=ti({isDisabled:o,onFocusChange:u}),{focusWithinProps:d}=ni({isDisabled:!o,onFocusWithinChange:u});return{isFocused:i,isFocusVisible:l,focusProps:o?d:p}}var Ms=require("react");var Xo=require("react");var ii=class{constructor(){Oe(this,"current",this.detect());Oe(this,"handoffState","pending");Oe(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},Ue=new ii;function Pe(e){var n,t;return Ue.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?(t=(n=e.current)==null?void 0:n.ownerDocument)!=null?t:document:null:document}var Ko=require("react");function Dt(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function he(){let e=[],n={addEventListener(t,o,r,i){return t.addEventListener(o,r,i),n.add(()=>t.removeEventListener(o,r,i))},requestAnimationFrame(...t){let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(...t){let o={current:!0};return Dt(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(t,o,r){let i=t.style.getPropertyValue(o);return Object.assign(t.style,{[o]:r}),this.add(()=>{Object.assign(t.style,{[o]:i})})},group(t){let o=he();return t(o),this.add(()=>o.dispose())},add(t){return e.includes(t)||e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let r of e.splice(o,1))r()}},dispose(){for(let t of e.splice(0))t()}};return n}function Se(){let[e]=(0,Ko.useState)(he);return(0,Ko.useEffect)(()=>()=>e.dispose(),[e]),e}var Ds=le(require("react"),1);var Os=require("react");var zo=require("react");var j=(e,n)=>{Ue.isServer?(0,zo.useEffect)(e,n):(0,zo.useLayoutEffect)(e,n)};function me(e){let n=(0,Os.useRef)(e);return j(()=>{n.current=e},[e]),n}var E=function(n){let t=me(n);return Ds.default.useCallback((...o)=>t.current(...o),[t])};function fc(e){let n=e.width/2,t=e.height/2;return{top:e.clientY-t,right:e.clientX+n,bottom:e.clientY+t,left:e.clientX-n}}function dc(e,n){return!(!e||!n||e.right<n.left||e.left>n.right||e.bottom<n.top||e.top>n.bottom)}function Ce({disabled:e=!1}={}){let n=(0,Xo.useRef)(null),[t,o]=(0,Xo.useState)(!1),r=Se(),i=E(()=>{n.current=null,o(!1),r.dispose()}),s=E(l=>{if(r.dispose(),n.current===null){n.current=l.currentTarget,o(!0);{let a=Pe(l.currentTarget);r.addEventListener(a,"pointerup",i,!1),r.addEventListener(a,"pointermove",c=>{if(n.current){let u=fc(c);o(dc(u,n.current.getBoundingClientRect()))}},!1),r.addEventListener(a,"pointercancel",i,!1)}}});return{pressed:t,pressProps:e?{}:{onPointerDown:s,onPointerUp:i,onClick:i}}}var vn=le(require("react"),1),Ls=(0,vn.createContext)(void 0);function ge(){return(0,vn.useContext)(Ls)}function Yo({value:e,children:n}){return vn.default.createElement(Ls.Provider,{value:e},n)}var Me=le(require("react"),1);function co(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}function q(e,n,...t){if(e in n){let r=n[e];return typeof r=="function"?r(...t):r}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,q),o}function N(){let e=mc();return(0,Me.useCallback)(n=>pc({mergeRefs:e,...n}),[e])}function pc({ourProps:e,theirProps:n,slot:t,defaultTag:o,features:r,visible:i=!0,name:s,mergeRefs:l}){l=l!=null?l:Tc;let a=Is(n,e);if(i)return qo(a,t,o,s,l);let c=r!=null?r:0;if(c&2){let{static:u=!1,...p}=a;if(u)return qo(p,t,o,s,l)}if(c&1){let{unmount:u=!0,...p}=a;return q(u?0:1,{[0](){return null},[1](){return qo({...p,hidden:!0,style:{display:"none"}},t,o,s,l)}})}return qo(a,t,o,s,l)}function qo(e,n={},t,o,r){let{as:i=t,children:s,refName:l="ref",...a}=si(e,["unmount","static"]),c=e.ref!==void 0?{[l]:e.ref}:{},u=typeof s=="function"?s(n):s;"className"in a&&a.className&&typeof a.className=="function"&&(a.className=a.className(n)),a["aria-labelledby"]&&a["aria-labelledby"]===a.id&&(a["aria-labelledby"]=void 0);let p={};if(n){let d=!1,f=[];for(let[m,T]of Object.entries(n))typeof T=="boolean"&&(d=!0),T===!0&&f.push(m.replace(/([A-Z])/g,b=>`-${b.toLowerCase()}`));if(d){p["data-headlessui-state"]=f.join(" ");for(let m of f)p[`data-${m}`]=""}}if(i===Me.Fragment&&(Object.keys(Tt(a)).length>0||Object.keys(Tt(p)).length>0))if(!(0,Me.isValidElement)(u)||Array.isArray(u)&&u.length>1){if(Object.keys(Tt(a)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Tt(a)).concat(Object.keys(Tt(p))).map(d=>`  - ${d}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(d=>`  - ${d}`).join(`
`)].join(`
`))}else{let d=u.props,f=d==null?void 0:d.className,m=typeof f=="function"?(...g)=>co(f(...g),a.className):co(f,a.className),T=m?{className:m}:{},b=Is(u.props,Tt(si(a,["ref"])));for(let g in p)g in b&&delete p[g];return(0,Me.cloneElement)(u,Object.assign({},b,p,c,{ref:r(bc(u),c.ref)},T))}return(0,Me.createElement)(i,Object.assign({},si(a,["ref"]),i!==Me.Fragment&&c,i!==Me.Fragment&&p),u)}function mc(){let e=(0,Me.useRef)([]),n=(0,Me.useCallback)(t=>{for(let o of e.current)o!=null&&(typeof o=="function"?o(t):o.current=t)},[]);return(...t)=>{if(!t.every(o=>o==null))return e.current=t,n}}function Tc(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function Is(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];if(n.disabled||n["aria-disabled"])for(let r in t)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(t[r]=[i=>{var s;return(s=i==null?void 0:i.preventDefault)==null?void 0:s.call(i)}]);for(let r in t)Object.assign(n,{[r](i,...s){let l=t[r];for(let a of l){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;a(i,...s)}}});return n}function ae(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];for(let r in t)Object.assign(n,{[r](...i){let s=t[r];for(let l of s)l==null||l(...i)}});return n}function _(e){var n;return Object.assign((0,Me.forwardRef)(e),{displayName:(n=e.displayName)!=null?n:e.name})}function Tt(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function si(e,n=[]){let t=Object.assign({},e);for(let o of n)o in t&&delete t[o];return t}function bc(e){return Me.default.version.split(".")[0]>="19"?e.props.ref:e.ref}var gc="button";function yc(e,n){var T;let t=ge(),{disabled:o=t||!1,autoFocus:r=!1,...i}=e,{isFocusVisible:s,focusProps:l}=ce({autoFocus:r}),{isHovered:a,hoverProps:c}=fe({isDisabled:o}),{pressed:u,pressProps:p}=Ce({disabled:o}),d=ae({ref:n,type:(T=i.type)!=null?T:"button",disabled:o||void 0,autoFocus:r},l,c,p),f=(0,Ms.useMemo)(()=>({disabled:o,hover:a,focus:s,active:u,autofocus:r}),[o,a,s,u,r]);return N()({ourProps:d,theirProps:i,slot:f,defaultTag:gc,name:"Button"})}var li=_(yc);var ht=le(require("react"),1);var En=require("react");function bt(e,n,t){let[o,r]=(0,En.useState)(t),i=e!==void 0,s=(0,En.useRef)(i),l=(0,En.useRef)(!1),a=(0,En.useRef)(!1);return i&&!s.current&&!l.current?(l.current=!0,s.current=i,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!i&&s.current&&!a.current&&(a.current=!0,s.current=i,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[i?e:o,E(c=>(i||r(c),n==null?void 0:n(c)))]}var Fs=require("react");function gt(e){let[n]=(0,Fs.useState)(e);return n}var J=require("react");var De=le(require("react"),1),$s=require("react-dom");function ai(e={},n=null,t=[]){for(let[o,r]of Object.entries(e))_s(t,ws(n,o),r);return t}function ws(e,n){return e?e+"["+n+"]":n}function _s(e,n,t){if(Array.isArray(t))for(let[o,r]of t.entries())_s(e,ws(n,o.toString()),r);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):ai(t,n,e)}function Ut(e){var t,o;let n=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(n){for(let r of n.elements)if(r!==e&&(r.tagName==="INPUT"&&r.type==="submit"||r.tagName==="BUTTON"&&r.type==="submit"||r.nodeName==="INPUT"&&r.type==="image")){r.click();return}(o=n.requestSubmit)==null||o.call(n)}}var hc="span";function vc(e,n){var s;let{features:t=1,...o}=e,r={ref:n,"aria-hidden":(t&2)===2?!0:(s=o["aria-hidden"])!=null?s:void 0,hidden:(t&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(t&4)===4&&(t&2)!==2&&{display:"none"}}};return N()({ourProps:r,theirProps:o,slot:{},defaultTag:hc,name:"Hidden"})}var ke=_(vc);var ks=(0,De.createContext)(null);function Hs(e){let[n,t]=(0,De.useState)(null);return De.default.createElement(ks.Provider,{value:{target:n}},e.children,De.default.createElement(ke,{features:4,ref:t}))}function Ec({children:e}){let n=(0,De.useContext)(ks);if(!n)return De.default.createElement(De.default.Fragment,null,e);let{target:t}=n;return t?(0,$s.createPortal)(De.default.createElement(De.default.Fragment,null,e),t):null}function yt({data:e,form:n,disabled:t,onReset:o,overrides:r}){let[i,s]=(0,De.useState)(null),l=Se();return(0,De.useEffect)(()=>{if(o&&i)return l.addEventListener(i,"reset",o)},[i,n,o]),De.default.createElement(Ec,null,De.default.createElement(xc,{setForm:s,formId:n}),ai(e).map(([a,c])=>De.default.createElement(ke,{features:4,...Tt({key:a,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:t,name:a,value:c,...r})})))}function xc({setForm:e,formId:n}){return(0,De.useEffect)(()=>{if(n){let t=document.getElementById(n);t&&e(t)}},[e,n]),n?null:De.default.createElement(ke,{features:4,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let o=t.closest("form");o&&e(o)}})}var xn=le(require("react"),1),Ns=(0,xn.createContext)(void 0);function we(){return(0,xn.useContext)(Ns)}function Bs({id:e,children:n}){return xn.default.createElement(Ns.Provider,{value:e},n)}function fo(e){return typeof e!="object"||e===null?!1:"nodeType"in e}function ut(e){return fo(e)&&"tagName"in e}function Te(e){return ut(e)&&"accessKey"in e}function Le(e){return ut(e)&&"tabIndex"in e}function Gs(e){return ut(e)&&"style"in e}function Us(e){return Te(e)&&e.nodeName==="IFRAME"}function on(e){return Te(e)&&e.nodeName==="INPUT"}function po(e){return Te(e)&&e.nodeName==="LABEL"}function Vs(e){return Te(e)&&e.nodeName==="FIELDSET"}function ui(e){return Te(e)&&e.nodeName==="LEGEND"}function Ws(e){return ut(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]'):!1}function _e(e){let n=e.parentElement,t=null;for(;n&&!Vs(n);)ui(n)&&(t=n),n=n.parentElement;let o=(n==null?void 0:n.getAttribute("disabled"))==="";return o&&Pc(t)?!1:o}function Pc(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(ui(n))return!1;n=n.previousElementSibling}return!0}var qe=le(require("react"),1);var Qo=require("react");var js=Symbol();function Pn(e,n=!0){return Object.assign(e,{[js]:n})}function z(...e){let n=(0,Qo.useRef)(e);(0,Qo.useEffect)(()=>{n.current=e},[e]);let t=E(o=>{for(let r of n.current)r!=null&&(typeof r=="function"?r(o):r.current=o)});return e.every(o=>o==null||(o==null?void 0:o[js]))?void 0:t}var Zo=(0,qe.createContext)(null);Zo.displayName="DescriptionContext";function Ks(){let e=(0,qe.useContext)(Zo);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Ks),n}return e}function Ne(){var e,n;return(n=(e=(0,qe.useContext)(Zo))==null?void 0:e.value)!=null?n:void 0}function ct(){let[e,n]=(0,qe.useState)([]);return[e.length>0?e.join(" "):void 0,(0,qe.useMemo)(()=>function(o){let r=E(s=>(n(l=>[...l,s]),()=>n(l=>{let a=l.slice(),c=a.indexOf(s);return c!==-1&&a.splice(c,1),a}))),i=(0,qe.useMemo)(()=>({register:r,slot:o.slot,name:o.name,props:o.props,value:o.value}),[r,o.slot,o.name,o.props,o.value]);return qe.default.createElement(Zo.Provider,{value:i},o.children)},[n])]}var Rc="p";function Sc(e,n){let t=(0,J.useId)(),o=ge(),{id:r=`headlessui-description-${t}`,...i}=e,s=Ks(),l=z(n);j(()=>s.register(r),[r,s.register]);let a=o||!1,c=(0,qe.useMemo)(()=>({...s.slot,disabled:a}),[s.slot,a]),u={ref:l,...s.props,id:r};return N()({ourProps:u,theirProps:i,slot:c,defaultTag:Rc,name:s.name||"Description"})}var Cc=_(Sc),Lt=Object.assign(Cc,{});var Je=le(require("react"),1);var er=(0,Je.createContext)(null);er.displayName="LabelContext";function tr(){let e=(0,Je.useContext)(er);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,tr),n}return e}function Fe(e){var t,o,r;let n=(o=(t=(0,Je.useContext)(er))==null?void 0:t.value)!=null?o:void 0;return((r=e==null?void 0:e.length)!=null?r:0)>0?[n,...e].filter(Boolean).join(" "):n}function Be({inherit:e=!1}={}){let n=Fe(),[t,o]=(0,Je.useState)([]),r=e?[n,...t].filter(Boolean):t;return[r.length>0?r.join(" "):void 0,(0,Je.useMemo)(()=>function(s){let l=E(c=>(o(u=>[...u,c]),()=>o(u=>{let p=u.slice(),d=p.indexOf(c);return d!==-1&&p.splice(d,1),p}))),a=(0,Je.useMemo)(()=>({register:l,slot:s.slot,name:s.name,props:s.props,value:s.value}),[l,s.slot,s.name,s.props,s.value]);return Je.default.createElement(er.Provider,{value:a},s.children)},[o])]}var Ac="label";function Oc(e,n){var b;let t=(0,J.useId)(),o=tr(),r=we(),i=ge(),{id:s=`headlessui-label-${t}`,htmlFor:l=r!=null?r:(b=o.props)==null?void 0:b.htmlFor,passive:a=!1,...c}=e,u=z(n);j(()=>o.register(s),[s,o.register]);let p=E(g=>{let h=g.currentTarget;if(!(g.target!==g.currentTarget&&Ws(g.target))&&(po(h)&&g.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(g),po(h))){let y=document.getElementById(h.htmlFor);if(y){let v=y.getAttribute("disabled");if(v==="true"||v==="")return;let x=y.getAttribute("aria-disabled");if(x==="true"||x==="")return;(on(y)&&(y.type==="file"||y.type==="radio"||y.type==="checkbox")||y.role==="radio"||y.role==="checkbox"||y.role==="switch")&&y.click(),y.focus({preventScroll:!0})}}}),d=i||!1,f=(0,Je.useMemo)(()=>({...o.slot,disabled:d}),[o.slot,d]),m={ref:u,...o.props,id:s,htmlFor:l,onClick:p};return a&&("onClick"in m&&(delete m.htmlFor,delete m.onClick),"onClick"in c&&delete c.onClick),N()({ourProps:m,theirProps:c,slot:f,defaultTag:l?Ac:"div",name:o.name||"Label"})}var Dc=_(Oc),nt=Object.assign(Dc,{});var Lc="span";function Ic(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-checkbox-${t}`,disabled:s=r||!1,autoFocus:l=!1,checked:a,defaultChecked:c,onChange:u,name:p,value:d,form:f,indeterminate:m=!1,tabIndex:T=0,...b}=e,g=gt(c),[h,y]=bt(a,u,g!=null?g:!1),v=Fe(),x=Ne(),R=Se(),[$,A]=(0,ht.useState)(!1),B=E(()=>{A(!0),y==null||y(!h),R.nextFrame(()=>{A(!1)})}),S=E(Y=>{if(_e(Y.currentTarget))return Y.preventDefault();Y.preventDefault(),B()}),C=E(Y=>{Y.key===" "?(Y.preventDefault(),B()):Y.key==="Enter"&&Ut(Y.currentTarget)}),L=E(Y=>Y.preventDefault()),{isFocusVisible:O,focusProps:P}=ce({autoFocus:l}),{isHovered:V,hoverProps:k}=fe({isDisabled:s}),{pressed:G,pressProps:ne}=Ce({disabled:s}),F=ae({ref:n,id:i,role:"checkbox","aria-checked":m?"mixed":h?"true":"false","aria-labelledby":v,"aria-describedby":x,"aria-disabled":s?!0:void 0,indeterminate:m?"true":void 0,tabIndex:s?void 0:T,onKeyUp:s?void 0:C,onKeyPress:s?void 0:L,onClick:s?void 0:S},P,k,ne),I=(0,ht.useMemo)(()=>({checked:h,disabled:s,hover:V,focus:O,active:G,indeterminate:m,changing:$,autofocus:l}),[h,m,s,V,O,G,$,l]),U=(0,ht.useCallback)(()=>{if(g!==void 0)return y==null?void 0:y(g)},[y,g]),W=N();return ht.default.createElement(ht.default.Fragment,null,p!=null&&ht.default.createElement(yt,{disabled:s,data:{[p]:d||"on"},overrides:{type:"checkbox",checked:h},form:f,onReset:U}),W({ourProps:F,theirProps:b,slot:I,defaultTag:Lc,name:"Checkbox"}))}var Mc=_(Ic);var Xs=le(require("react"),1);var Rn=le(require("react"),1),zs=(0,Rn.createContext)(()=>{});function nr(){return(0,Rn.useContext)(zs)}function rn({value:e,children:n}){return Rn.default.createElement(zs.Provider,{value:e},n)}function Fc(e,n){let t=nr();return Xs.default.createElement(li,{ref:n,...ae({onClick:t},e)})}var wc=_(Fc);var Vt=le(require("react"),1),il=require("react-dom");function sn(e,n,t){var s;let o=(s=t.initialDeps)!=null?s:[],r;function i(){var l,a,c,u;let p;t.key&&((l=t.debug)!=null&&l.call(t))&&(p=Date.now());let d=e();if(!(d.length!==o.length||d.some((T,b)=>o[b]!==T)))return r;o=d;let m;if(t.key&&((a=t.debug)!=null&&a.call(t))&&(m=Date.now()),r=n(...d),t.key&&((c=t.debug)!=null&&c.call(t))){let T=Math.round((Date.now()-p)*100)/100,b=Math.round((Date.now()-m)*100)/100,g=b/16,h=(y,v)=>{for(y=String(y);y.length<v;)y=" "+y;return y};console.info(`%c\u23F1 ${h(b,5)} /${h(T,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(u=t==null?void 0:t.onChange)==null||u.call(t,r),r}return i.updateDeps=l=>{o=l},i}function ci(e,n){if(e===void 0)throw new Error(`Unexpected undefined${n?`: ${n}`:""}`);return e}var Ys=(e,n)=>Math.abs(e-n)<=1,qs=(e,n,t)=>{let o;return function(...r){e.clearTimeout(o),o=e.setTimeout(()=>n.apply(this,r),t)}};var Js=e=>{let{offsetWidth:n,offsetHeight:t}=e;return{width:n,height:t}},_c=e=>e,$c=e=>{let n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),o=[];for(let r=n;r<=t;r++)o.push(r);return o},el=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=s=>{let{width:l,height:a}=s;n({width:Math.round(l),height:Math.round(a)})};if(r(Js(t)),!o.ResizeObserver)return()=>{};let i=new o.ResizeObserver(s=>{let l=()=>{let a=s[0];if(a!=null&&a.borderBoxSize){let c=a.borderBoxSize[0];if(c){r({width:c.inlineSize,height:c.blockSize});return}}r(Js(t))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()});return i.observe(t,{box:"border-box"}),()=>{i.unobserve(t)}},Qs={passive:!0};var Zs=typeof window=="undefined"?!0:"onscrollend"in window,tl=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=0,i=e.options.useScrollendEvent&&Zs?()=>{}:qs(o,()=>{n(r,!1)},e.options.isScrollingResetDelay),s=u=>()=>{let{horizontal:p,isRtl:d}=e.options;r=p?t.scrollLeft*(d&&-1||1):t.scrollTop,i(),n(r,u)},l=s(!0),a=s(!1);a(),t.addEventListener("scroll",l,Qs);let c=e.options.useScrollendEvent&&Zs;return c&&t.addEventListener("scrollend",a,Qs),()=>{t.removeEventListener("scroll",l),c&&t.removeEventListener("scrollend",a)}};var kc=(e,n,t)=>{if(n!=null&&n.borderBoxSize){let o=n.borderBoxSize[0];if(o)return Math.round(o[t.options.horizontal?"inlineSize":"blockSize"])}return e[t.options.horizontal?"offsetWidth":"offsetHeight"]};var nl=(e,{adjustments:n=0,behavior:t},o)=>{var r,i;let s=e+n;(i=(r=o.scrollElement)==null?void 0:r.scrollTo)==null||i.call(r,{[o.options.horizontal?"left":"top"]:s,behavior:t})},or=class{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null,o=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(r=>{r.forEach(i=>{let s=()=>{this._measureElement(i.target,i)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(s):s()})}));return{disconnect:()=>{var r;(r=o())==null||r.disconnect(),t=null},observe:r=>{var i;return(i=o())==null?void 0:i.observe(r,{box:"border-box"})},unobserve:r=>{var i;return(i=o())==null?void 0:i.unobserve(r)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([o,r])=>{typeof r=="undefined"&&delete t[o]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:_c,rangeExtractor:$c,onChange:()=>{},measureElement:kc,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...t}},this.notify=t=>{var o,r;(r=(o=this.options).onChange)==null||r.call(o,this,t)},this.maybeNotify=sn(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),t=>{this.notify(t)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var r;var t;let o=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==o){if(this.cleanup(),!o){this.maybeNotify();return}this.scrollElement=o,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(r=(t=this.scrollElement)==null?void 0:t.window)!=null?r:null,this.elementsCache.forEach(i=>{this.observer.observe(i)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,i=>{this.scrollRect=i,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(i,s)=>{this.scrollAdjustments=0,this.scrollDirection=s?this.getScrollOffset()<i?"forward":"backward":null,this.scrollOffset=i,this.isScrolling=s,this.maybeNotify()}))}},this.getSize=()=>{var t;return this.options.enabled?(this.scrollRect=(t=this.scrollRect)!=null?t:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var t;return this.options.enabled?(this.scrollOffset=(t=this.scrollOffset)!=null?t:typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(t,o)=>{let r=new Map,i=new Map;for(let s=o-1;s>=0;s--){let l=t[s];if(r.has(l.lane))continue;let a=i.get(l.lane);if(a==null||l.end>a.end?i.set(l.lane,l):l.end<a.end&&r.set(l.lane,!0),r.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((s,l)=>s.end===l.end?s.index-l.index:s.end-l.end)[0]:void 0},this.getMeasurementOptions=sn(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,o,r,i,s)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s}),{key:!1}),this.getMeasurements=sn(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s},l)=>{if(!s)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(u=>{this.itemSizeCache.set(u.key,u.size)}));let a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let c=this.measurementsCache.slice(0,a);for(let u=a;u<t;u++){let p=i(u),d=this.options.lanes===1?c[u-1]:this.getFurthestMeasurement(c,u),f=d?d.end+this.options.gap:o+r,m=l.get(p),T=typeof m=="number"?m:this.options.estimateSize(u),b=f+T,g=d?d.lane:u%this.options.lanes;c[u]={index:u,start:f,size:T,end:b,key:p,lane:g}}return this.measurementsCache=c,c},{key:!1,debug:()=>this.options.debug}),this.calculateRange=sn(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(t,o,r,i)=>this.range=t.length>0&&o>0?Hc({measurements:t,outerSize:o,scrollOffset:r,lanes:i}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=sn(()=>{let t=null,o=null,r=this.calculateRange();return r&&(t=r.startIndex,o=r.endIndex),this.maybeNotify.updateDeps([this.isScrolling,t,o]),[this.options.rangeExtractor,this.options.overscan,this.options.count,t,o]},(t,o,r,i,s)=>i===null||s===null?[]:t({startIndex:i,endIndex:s,overscan:o,count:r}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=t=>{let o=this.options.indexAttribute,r=t.getAttribute(o);return r?parseInt(r,10):(console.warn(`Missing attribute name '${o}={index}' on measured element.`),-1)},this._measureElement=(t,o)=>{let r=this.indexFromElement(t),i=this.measurementsCache[r];if(!i)return;let s=i.key,l=this.elementsCache.get(s);l!==t&&(l&&this.observer.unobserve(l),this.observer.observe(t),this.elementsCache.set(s,t)),t.isConnected&&this.resizeItem(r,this.options.measureElement(t,o,this))},this.resizeItem=(t,o)=>{var l;let r=this.measurementsCache[t];if(!r)return;let i=(l=this.itemSizeCache.get(r.key))!=null?l:r.size,s=o-i;s!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(r,s,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=s,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,o)),this.notify(!1))},this.measureElement=t=>{if(!t){this.elementsCache.forEach((o,r)=>{o.isConnected||(this.observer.unobserve(o),this.elementsCache.delete(r))});return}this._measureElement(t,void 0)},this.getVirtualItems=sn(()=>[this.getVirtualIndexes(),this.getMeasurements()],(t,o)=>{let r=[];for(let i=0,s=t.length;i<s;i++){let l=t[i],a=o[l];r.push(a)}return r},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{let o=this.getMeasurements();if(o.length!==0)return ci(o[ol(0,o.length-1,r=>ci(o[r]).start,t)])},this.getOffsetForAlignment=(t,o,r=0)=>{let i=this.getSize(),s=this.getScrollOffset();o==="auto"&&(o=t>=s+i?"end":"start"),o==="center"?t+=(r-i)/2:o==="end"&&(t-=i);let l=this.getTotalSize()-i;return Math.max(Math.min(l,t),0)},this.getOffsetForIndex=(t,o="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));let r=this.measurementsCache[t];if(!r)return;let i=this.getSize(),s=this.getScrollOffset();if(o==="auto")if(r.end>=s+i-this.options.scrollPaddingEnd)o="end";else if(r.start<=s+this.options.scrollPaddingStart)o="start";else return[s,o];let l=o==="end"?r.end+this.options.scrollPaddingEnd:r.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(l,o,r.size),o]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:o="start",behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,o),{adjustments:void 0,behavior:r})},this.scrollToIndex=(t,{align:o="auto",behavior:r}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(t,o);if(!i)return;let[s,l]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:r}),r!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){let c=this.getOffsetForIndex(t,l);if(!c)return;let[u]=c,p=this.getScrollOffset();Ys(u,p)||this.scrollToIndex(t,{align:l,behavior:r})}else this.scrollToIndex(t,{align:l,behavior:r})}))},this.scrollBy=(t,{behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:o})},this.getTotalSize=()=>{var i;var t;let o=this.getMeasurements(),r;if(o.length===0)r=this.options.paddingStart;else if(this.options.lanes===1)r=(i=(t=o[o.length-1])==null?void 0:t.end)!=null?i:0;else{let s=Array(this.options.lanes).fill(null),l=o.length-1;for(;l>=0&&s.some(a=>a===null);){let a=o[l];s[a.lane]===null&&(s[a.lane]=a.end),l--}r=Math.max(...s.filter(a=>a!==null))}return Math.max(r-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(t,{adjustments:o,behavior:r})=>{this.options.scrollToFn(t,{behavior:r,adjustments:o},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(n)}},ol=(e,n,t,o)=>{for(;e<=n;){let r=(e+n)/2|0,i=t(r);if(i<o)e=r+1;else if(i>o)n=r-1;else return r}return e>0?e-1:0};function Hc({measurements:e,outerSize:n,scrollOffset:t,lanes:o}){let r=e.length-1,i=a=>e[a].start;if(e.length<=o)return{startIndex:0,endIndex:r};let s=ol(0,r,i,t),l=s;if(o===1)for(;l<r&&e[l].end<t+n;)l++;else if(o>1){let a=Array(o).fill(0);for(;l<r&&a.some(u=>u<t+n);){let u=e[l];a[u.lane]=u.end,l++}let c=Array(o).fill(t+n);for(;s>=0&&c.some(u=>u>=t);){let u=e[s];c[u.lane]=u.start,s--}s=Math.max(0,s-s%o),l=Math.min(r,l+(o-1-l%o))}return{startIndex:s,endIndex:l}}var rl=typeof document!="undefined"?Vt.useLayoutEffect:Vt.useEffect;function Nc(e){let n=Vt.useReducer(()=>({}),{})[1],t={...e,onChange:(r,i)=>{var s;i?(0,il.flushSync)(n):n(),(s=e.onChange)==null||s.call(e,r,i)}},[o]=Vt.useState(()=>new or(t));return o.setOptions(t),rl(()=>o._didMount(),[]),rl(()=>o._willUpdate()),o}function sl(e){return Nc({observeElementRect:el,observeElementOffset:tl,scrollToFn:nl,...e})}var Z=le(require("react"),1),Tn=require("react-dom");var ll=require("react");function Bc(e,n){return e!==null&&n!==null&&typeof e=="object"&&typeof n=="object"&&"id"in e&&"id"in n?e.id===n.id:e===n}function Sn(e=Bc){return(0,ll.useCallback)((n,t)=>{if(typeof e=="string"){let o=e;return(n==null?void 0:n[o])===(t==null?void 0:t[o])}return e(n,t)},[e])}var rr=require("react");function Gc(e){if(e===null)return{width:0,height:0};let{width:n,height:t}=e.getBoundingClientRect();return{width:n,height:t}}function It(e,n=!1){let[t,o]=(0,rr.useReducer)(()=>({}),{}),r=(0,rr.useMemo)(()=>Gc(e),[e,t]);return j(()=>{if(!e)return;let i=new ResizeObserver(o);return i.observe(e),()=>{i.disconnect()}},[e]),n?{width:`${r.width}px`,height:`${r.height}px`}:r}var ir=require("react");var Cn=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t),this.set(t,o)),o}};var ft,An,On,ot=class{constructor(n){Ho(this,ft,{});Ho(this,An,new Cn(()=>new Set));Ho(this,On,new Set);Oe(this,"disposables",he());Br(this,ft,n),Ue.isServer&&this.disposables.microTask(()=>{this.dispose()})}dispose(){this.disposables.dispose()}get state(){return Xe(this,ft)}subscribe(n,t){if(Ue.isServer)return()=>{};let o={selector:n,callback:t,current:n(Xe(this,ft))};return Xe(this,On).add(o),this.disposables.add(()=>{Xe(this,On).delete(o)})}on(n,t){return Ue.isServer?()=>{}:(Xe(this,An).get(n).add(t),this.disposables.add(()=>{Xe(this,An).get(n).delete(t)}))}send(n){let t=this.reduce(Xe(this,ft),n);if(t!==Xe(this,ft)){Br(this,ft,t);for(let o of Xe(this,On)){let r=o.selector(Xe(this,ft));di(o.current,r)||(o.current=r,o.callback(r))}for(let o of Xe(this,An).get(n.type))o(Xe(this,ft),n)}}};ft=new WeakMap,An=new WeakMap,On=new WeakMap;function di(e,n){return Object.is(e,n)?!0:typeof e!="object"||e===null||typeof n!="object"||n===null?!1:Array.isArray(e)&&Array.isArray(n)?e.length!==n.length?!1:fi(e[Symbol.iterator](),n[Symbol.iterator]()):e instanceof Map&&n instanceof Map||e instanceof Set&&n instanceof Set?e.size!==n.size?!1:fi(e.entries(),n.entries()):al(e)&&al(n)?fi(Object.entries(e)[Symbol.iterator](),Object.entries(n)[Symbol.iterator]()):!1}function fi(e,n){do{let t=e.next(),o=n.next();if(t.done&&o.done)return!0;if(t.done||o.done||!Object.is(t.value,o.value))return!1}while(!0)}function al(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;let n=Object.getPrototypeOf(e);return n===null||Object.getPrototypeOf(n)===null}function ln(e){let[n,t]=e(),o=he();return(...r)=>{n(...r),o.dispose(),o.microTask(t)}}var Uc={[0](e,n){let t=n.id,o=e.stack,r=e.stack.indexOf(t);if(r!==-1){let i=e.stack.slice();return i.splice(r,1),i.push(t),o=i,{...e,stack:o}}return{...e,stack:[...e.stack,t]}},[1](e,n){let t=n.id,o=e.stack.indexOf(t);if(o===-1)return e;let r=e.stack.slice();return r.splice(o,1),{...e,stack:r}}},mo=class extends ot{constructor(){super(...arguments);Oe(this,"actions",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});Oe(this,"selectors",{isTop:(t,o)=>t.stack[t.stack.length-1]===o,inStack:(t,o)=>t.stack.includes(o)})}static new(){return new mo({stack:[]})}reduce(t,o){return q(o.type,Uc,t,o)}},$e=new Cn(()=>mo.new());var ul=require("use-sync-external-store/with-selector");function ee(e,n,t=di){return(0,ul.useSyncExternalStoreWithSelector)(E(o=>e.subscribe(Vc,o)),E(()=>e.state),E(()=>e.state),E(n),t)}function Vc(e){return e}function Mt(e,n){let t=(0,ir.useId)(),o=$e.get(n),[r,i]=ee(o,(0,ir.useCallback)(s=>[o.selectors.isTop(s,t),o.selectors.inStack(s,t)],[o,t]));return j(()=>{if(e)return o.actions.push(t),()=>o.actions.pop(t)},[o,e,t]),e?i?r:!0:!1}var pi=new Map,To=new Map;function cl(e){var t;let n=(t=To.get(e))!=null?t:0;return To.set(e,n+1),n!==0?()=>fl(e):(pi.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0,()=>fl(e))}function fl(e){var o;let n=(o=To.get(e))!=null?o:1;if(n===1?To.delete(e):To.set(e,n-1),n!==1)return;let t=pi.get(e);t&&(t["aria-hidden"]===null?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert,pi.delete(e))}function Wt(e,{allowed:n,disallowed:t}={}){let o=Mt(e,"inert-others");j(()=>{var s,l;if(!o)return;let r=he();for(let a of(s=t==null?void 0:t())!=null?s:[])a&&r.add(cl(a));let i=(l=n==null?void 0:n())!=null?l:[];for(let a of i){if(!a)continue;let c=Pe(a);if(!c)continue;let u=a.parentElement;for(;u&&u!==c.body;){for(let p of u.children)i.some(d=>p.contains(d))||r.add(cl(p));u=u.parentElement}}return r.dispose},[o,n,t])}var dl=require("react");function vt(e,n,t){let o=me(r=>{let i=r.getBoundingClientRect();i.x===0&&i.y===0&&i.width===0&&i.height===0&&t()});(0,dl.useEffect)(()=>{if(!e)return;let r=n===null?null:Te(n)?n:n.current;if(!r)return;let i=he();if(typeof ResizeObserver!="undefined"){let s=new ResizeObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}if(typeof IntersectionObserver!="undefined"){let s=new IntersectionObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}return()=>i.dispose()},[n,o,e])}var yo=require("react");var bo=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),Wc=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function an(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(bo)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function jc(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Wc)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Ft(e,n=0){var t;return e===((t=Pe(e))==null?void 0:t.body)?!1:q(n,{[0](){return e.matches(bo)},[1](){let o=e;for(;o!==null;){if(o.matches(bo))return!0;o=o.parentElement}return!1}})}function mi(e){let n=Pe(e);he().nextFrame(()=>{n&&Le(n.activeElement)&&!Ft(n.activeElement,0)&&dt(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function dt(e){e==null||e.focus({preventScroll:!0})}var Kc=["textarea","input"].join(",");function zc(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,Kc))!=null?t:!1}function Ve(e,n=t=>t){return e.slice().sort((t,o)=>{let r=n(t),i=n(o);if(r===null||i===null)return 0;let s=r.compareDocumentPosition(i);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function sr(e,n){return ve(an(),n,{relativeTo:e})}function ve(e,n,{sorted:t=!0,relativeTo:o=null,skipElements:r=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?t?Ve(e):e:n&64?jc(e):an(e);r.length>0&&s.length>1&&(s=s.filter(f=>!r.some(m=>m!=null&&"current"in m?(m==null?void 0:m.current)===f:m===f))),o=o!=null?o:i.activeElement;let l=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),a=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,s.indexOf(o))-1;if(n&4)return Math.max(0,s.indexOf(o))+1;if(n&8)return s.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=n&32?{preventScroll:!0}:{},u=0,p=s.length,d;do{if(u>=p||u+p<=0)return 0;let f=a+u;if(n&16)f=(f+p)%p;else{if(f<0)return 3;if(f>=p)return 1}d=s[f],d==null||d.focus(c),u+=l}while(d!==i.activeElement);return n&6&&zc(d)&&d.select(),2}function Ti(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Xc(){return/Android/gi.test(window.navigator.userAgent)}function go(){return Ti()||Xc()}var pl=require("react");function jt(e,n,t,o){let r=me(t);(0,pl.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return document.addEventListener(n,i,o),()=>document.removeEventListener(n,i,o)},[e,n,o])}var ml=require("react");function lr(e,n,t,o){let r=me(t);(0,ml.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return window.addEventListener(n,i,o),()=>window.removeEventListener(n,i,o)},[e,n,o])}var Tl=30;function Et(e,n,t){let o=me(t),r=(0,yo.useCallback)(function(a,c){if(a.defaultPrevented)return;let u=c(a);if(u===null||!u.getRootNode().contains(u)||!u.isConnected)return;let p=function d(f){return typeof f=="function"?d(f()):Array.isArray(f)||f instanceof Set?f:[f]}(n);for(let d of p)if(d!==null&&(d.contains(u)||a.composed&&a.composedPath().includes(d)))return;return!Ft(u,1)&&u.tabIndex!==-1&&a.preventDefault(),o.current(a,u)},[o,n]),i=(0,yo.useRef)(null);jt(e,"pointerdown",l=>{var a,c;go()||(i.current=((c=(a=l.composedPath)==null?void 0:a.call(l))==null?void 0:c[0])||l.target)},!0),jt(e,"pointerup",l=>{if(go()||!i.current)return;let a=i.current;return i.current=null,r(l,()=>a)},!0);let s=(0,yo.useRef)({x:0,y:0});jt(e,"touchstart",l=>{s.current.x=l.touches[0].clientX,s.current.y=l.touches[0].clientY},!0),jt(e,"touchend",l=>{let a={x:l.changedTouches[0].clientX,y:l.changedTouches[0].clientY};if(!(Math.abs(a.x-s.current.x)>=Tl||Math.abs(a.y-s.current.y)>=Tl))return r(l,()=>Le(l.target)?l.target:null)},!0),lr(e,"blur",l=>r(l,()=>Us(window.document.activeElement)?window.document.activeElement:null),!0)}var bl=require("react");function Re(...e){return(0,bl.useMemo)(()=>Pe(...e),[...e])}var ar=require("react");var Ge={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}},Yc=200,gl=5;function Dn(e,{trigger:n,action:t,close:o,select:r}){let i=(0,ar.useRef)(null),s=(0,ar.useRef)(null),l=(0,ar.useRef)(null);jt(e&&n!==null,"pointerdown",a=>{fo(a==null?void 0:a.target)&&n!=null&&n.contains(a.target)&&(s.current=a.x,l.current=a.y,i.current=a.timeStamp)}),jt(e&&n!==null,"pointerup",a=>{var p,d;let c=i.current;if(c===null||(i.current=null,!Le(a.target))||Math.abs(a.x-((p=s.current)!=null?p:a.x))<gl&&Math.abs(a.y-((d=l.current)!=null?d:a.y))<gl)return;let u=t(a);switch(u.kind){case 0:return;case 1:{a.timeStamp-c>Yc&&(r(u.target),o());break}case 2:{o();break}}},{capture:!0})}var hl=require("react");var yl=require("react");function Kt(e,n,t,o){let r=me(t);(0,yl.useEffect)(()=>{e=e!=null?e:window;function i(s){r.current(s)}return e.addEventListener(n,i,o),()=>e.removeEventListener(n,i,o)},[e,n,o])}function gi(e){let n=(0,hl.useRef)({value:"",selectionStart:null,selectionEnd:null});return Kt(e,"blur",t=>{let o=t.target;on(o)&&(n.current={value:o.value,selectionStart:o.selectionStart,selectionEnd:o.selectionEnd})}),E(()=>{if(document.activeElement!==e&&on(e)&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==n.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:t,selectionEnd:o}=n.current;t!==null&&o!==null&&e.setSelectionRange(t,o)}n.current={value:"",selectionStart:null,selectionEnd:null}}})}var vl=require("react");function Ke(e,n){return(0,vl.useMemo)(()=>{var o;if(e.type)return e.type;let t=(o=e.as)!=null?o:"button";if(typeof t=="string"&&t.toLowerCase()==="button"||(n==null?void 0:n.tagName)==="BUTTON"&&!n.hasAttribute("type"))return"button"},[e.type,e.as,n])}var El=require("react");function xl(e){return(0,El.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}function Pl(e,n){let t=e(),o=new Set;return{getSnapshot(){return t},subscribe(r){return o.add(r),()=>o.delete(r)},dispatch(r,...i){let s=n[r].call(t,...i);s&&(t=s,o.forEach(l=>l()))}}}function Rl(){let e;return{before({doc:n}){var r;let t=n.documentElement,o=(r=n.defaultView)!=null?r:window;e=Math.max(0,o.innerWidth-t.clientWidth)},after({doc:n,d:t}){let o=n.documentElement,r=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-r);t.style(o,"paddingRight",`${i}px`)}}}function Sl(){return Ti()?{before({doc:e,d:n,meta:t}){function o(r){return t.containers.flatMap(i=>i()).some(i=>i.contains(r))}n.microTask(()=>{var s;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let l=he();l.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>l.dispose()))}let r=(s=window.scrollY)!=null?s:window.pageYOffset,i=null;n.addEventListener(e,"click",l=>{if(Le(l.target))try{let a=l.target.closest("a");if(!a)return;let{hash:c}=new URL(a.href),u=e.querySelector(c);Le(u)&&!o(u)&&(i=u)}catch{}},!0),n.addEventListener(e,"touchstart",l=>{if(Le(l.target)&&Gs(l.target))if(o(l.target)){let a=l.target;for(;a.parentElement&&o(a.parentElement);)a=a.parentElement;n.style(a,"overscrollBehavior","contain")}else n.style(l.target,"touchAction","none")}),n.addEventListener(e,"touchmove",l=>{if(Le(l.target)){if(on(l.target))return;if(o(l.target)){let a=l.target;for(;a.parentElement&&a.dataset.headlessuiPortal!==""&&!(a.scrollHeight>a.clientHeight||a.scrollWidth>a.clientWidth);)a=a.parentElement;a.dataset.headlessuiPortal===""&&l.preventDefault()}else l.preventDefault()}},{passive:!1}),n.add(()=>{var a;let l=(a=window.scrollY)!=null?a:window.pageYOffset;r!==l&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function Cl(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function qc(e){let n={};for(let t of e)Object.assign(n,t(n));return n}var zt=Pl(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:he(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:qc(t)},r=[Sl(),Rl(),Cl()];r.forEach(({before:i})=>i==null?void 0:i(o)),r.forEach(({after:i})=>i==null?void 0:i(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});zt.subscribe(()=>{let e=zt.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)==="hidden",r=t.count!==0;(r&&!o||!r&&o)&&zt.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&zt.dispatch("TEARDOWN",t)}});function Al(e,n,t=()=>({containers:[]})){let o=xl(zt),r=n?o.get(n):void 0,i=r?r.count>0:!1;return j(()=>{if(!(!n||!e))return zt.dispatch("PUSH",n,t),()=>zt.dispatch("POP",n,t)},[e,n]),i}function xt(e,n,t=()=>[document.body]){let o=Mt(e,"scroll-lock");Al(o,n,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],t]}})}var Dl=require("react");function Ol(e){return[e.screenX,e.screenY]}function Ln(){let e=(0,Dl.useRef)([-1,-1]);return{wasMoved(n){let t=Ol(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=Ol(n)}}}var ho=require("react");var Xt=require("react");function Ll(e=0){let[n,t]=(0,Xt.useState)(e),o=(0,Xt.useCallback)(a=>t(a),[n]),r=(0,Xt.useCallback)(a=>t(c=>c|a),[n]),i=(0,Xt.useCallback)(a=>(n&a)===a,[n]),s=(0,Xt.useCallback)(a=>t(c=>c&~a),[t]),l=(0,Xt.useCallback)(a=>t(c=>c^a),[t]);return{flags:n,setFlag:o,addFlag:r,hasFlag:i,removeFlag:s,toggleFlag:l}}var Il,Ml;typeof process!="undefined"&&typeof globalThis!="undefined"&&typeof Element!="undefined"&&((Il=process==null?void 0:process.env)==null?void 0:Il["NODE_ENV"])==="test"&&typeof((Ml=Element==null?void 0:Element.prototype)==null?void 0:Ml.getAnimations)=="undefined"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});function Qe(e){let n={};for(let t in e)e[t]===!0&&(n[`data-${t}`]="");return n}function Ze(e,n,t,o){let[r,i]=(0,ho.useState)(t),{hasFlag:s,addFlag:l,removeFlag:a}=Ll(e&&r?3:0),c=(0,ho.useRef)(!1),u=(0,ho.useRef)(!1),p=Se();return j(()=>{var d;if(e){if(t&&i(!0),!n){t&&l(3);return}return(d=o==null?void 0:o.start)==null||d.call(o,t),Jc(n,{inFlight:c,prepare(){u.current?u.current=!1:u.current=c.current,c.current=!0,!u.current&&(t?(l(3),a(4)):(l(4),a(2)))},run(){u.current?t?(a(3),l(4)):(a(4),l(3)):t?a(1):l(1)},done(){var f;u.current&&typeof n.getAnimations=="function"&&n.getAnimations().length>0||(c.current=!1,a(7),t||i(!1),(f=o==null?void 0:o.end)==null||f.call(o,t))}})}},[e,t,n,p]),e?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[t,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function Jc(e,{prepare:n,run:t,done:o,inFlight:r}){let i=he();return Zc(e,{prepare:n,inFlight:r}),i.nextFrame(()=>{t(),i.requestAnimationFrame(()=>{i.add(Qc(e,o))})}),i.dispose}function Qc(e,n){var i,s;let t=he();if(!e)return t.dispose;let o=!1;t.add(()=>{o=!0});let r=(s=(i=e.getAnimations)==null?void 0:i.call(e).filter(l=>l instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),t.dispose):(Promise.allSettled(r.map(l=>l.finished)).then(()=>{o||n()}),t.dispose)}function Zc(e,{inFlight:n,prepare:t}){if(n!=null&&n.current){t();return}let o=e.style.transition;e.style.transition="none",t(),e.offsetHeight,e.style.transition=o}var vo=require("react");function ur(e,{container:n,accept:t,walk:o}){let r=(0,vo.useRef)(t),i=(0,vo.useRef)(o);(0,vo.useEffect)(()=>{r.current=t,i.current=o},[t,o]),j(()=>{if(!n||!e)return;let s=Pe(n);if(!s)return;let l=r.current,a=i.current,c=Object.assign(p=>l(p),{acceptNode:l}),u=s.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,c,!1);for(;u.nextNode();)a(u.currentNode)},[n,e,r,i])}var cr=require("react");function un(e,n){let t=(0,cr.useRef)([]),o=E(e);(0,cr.useEffect)(()=>{let r=[...t.current];for(let[i,s]of n.entries())if(t.current[i]!==s){let l=o(n,r);return t.current=n,l}},[o,...n])}var te=le(require("react"),1),Oo=require("react");function Fl(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Eo(e){return e instanceof Element||e instanceof Fl(e).Element}function wl(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(n=>{let{brand:t,version:o}=n;return t+"/"+o}).join(" "):navigator.userAgent}var Yt=Math.min,We=Math.max,Po=Math.round,Ro=Math.floor,wt=e=>({x:e,y:e}),ef={left:"right",right:"left",bottom:"top",top:"bottom"},tf={start:"end",end:"start"};function yi(e,n,t){return We(e,Yt(n,t))}function In(e,n){return typeof e=="function"?e(n):e}function _t(e){return e.split("-")[0]}function Mn(e){return e.split("-")[1]}function hi(e){return e==="x"?"y":"x"}function vi(e){return e==="y"?"height":"width"}function Fn(e){return["top","bottom"].includes(_t(e))?"y":"x"}function Ei(e){return hi(Fn(e))}function _l(e,n,t){t===void 0&&(t=!1);let o=Mn(e),r=Ei(e),i=vi(r),s=r==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[i]>n.floating[i]&&(s=xo(s)),[s,xo(s)]}function $l(e){let n=xo(e);return[fr(e),n,fr(n)]}function fr(e){return e.replace(/start|end/g,n=>tf[n])}function nf(e,n,t){let o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return t?n?r:o:n?o:r;case"left":case"right":return n?i:s;default:return[]}}function kl(e,n,t,o){let r=Mn(e),i=nf(_t(e),t==="start",o);return r&&(i=i.map(s=>s+"-"+r),n&&(i=i.concat(i.map(fr)))),i}function xo(e){return e.replace(/left|right|bottom|top/g,n=>ef[n])}function of(e){return{top:0,right:0,bottom:0,left:0,...e}}function Hl(e){return typeof e!="number"?of(e):{top:e,right:e,bottom:e,left:e}}function cn(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function Nl(e,n,t){let{reference:o,floating:r}=e,i=Fn(n),s=Ei(n),l=vi(s),a=_t(n),c=i==="y",u=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,d=o[l]/2-r[l]/2,f;switch(a){case"top":f={x:u,y:o.y-r.height};break;case"bottom":f={x:u,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:p};break;case"left":f={x:o.x-r.width,y:p};break;default:f={x:o.x,y:o.y}}switch(Mn(n)){case"start":f[s]-=d*(t&&c?-1:1);break;case"end":f[s]+=d*(t&&c?-1:1);break}return f}var Bl=async(e,n,t)=>{let{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=t,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(n)),c=await s.getElementRects({reference:e,floating:n,strategy:r}),{x:u,y:p}=Nl(c,o,a),d=o,f={},m=0;for(let T=0;T<l.length;T++){let{name:b,fn:g}=l[T],{x:h,y,data:v,reset:x}=await g({x:u,y:p,initialPlacement:o,placement:d,strategy:r,middlewareData:f,rects:c,platform:s,elements:{reference:e,floating:n}});if(u=h!=null?h:u,p=y!=null?y:p,f={...f,[b]:{...f[b],...v}},x&&m<=50){m++,typeof x=="object"&&(x.placement&&(d=x.placement),x.rects&&(c=x.rects===!0?await s.getElementRects({reference:e,floating:n,strategy:r}):x.rects),{x:u,y:p}=Nl(c,d,a)),T=-1;continue}}return{x:u,y:p,placement:d,strategy:r,middlewareData:f}};async function Pt(e,n){var t;n===void 0&&(n={});let{x:o,y:r,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:p="floating",altBoundary:d=!1,padding:f=0}=In(n,e),m=Hl(f),b=l[d?p==="floating"?"reference":"floating":p],g=cn(await i.getClippingRect({element:(t=await(i.isElement==null?void 0:i.isElement(b)))==null||t?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:a})),h=p==="floating"?{...s.floating,x:o,y:r}:s.reference,y=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),v=await(i.isElement==null?void 0:i.isElement(y))?await(i.getScale==null?void 0:i.getScale(y))||{x:1,y:1}:{x:1,y:1},x=cn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:h,offsetParent:y,strategy:a}):h);return{top:(g.top-x.top+m.top)/v.y,bottom:(x.bottom-g.bottom+m.bottom)/v.y,left:(g.left-x.left+m.left)/v.x,right:(x.right-g.right+m.right)/v.x}}var xi=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;let{placement:r,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:c}=n,{mainAxis:u=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:T=!0,...b}=In(e,n);if((t=i.arrow)!=null&&t.alignmentOffset)return{};let g=_t(r),h=_t(l)===l,y=await(a.isRTL==null?void 0:a.isRTL(c.floating)),v=d||(h||!T?[xo(l)]:$l(l));!d&&m!=="none"&&v.push(...kl(l,T,m,y));let x=[l,...v],R=await Pt(n,b),$=[],A=((o=i.flip)==null?void 0:o.overflows)||[];if(u&&$.push(R[g]),p){let L=_l(r,s,y);$.push(R[L[0]],R[L[1]])}if(A=[...A,{placement:r,overflows:$}],!$.every(L=>L<=0)){var B,S;let L=(((B=i.flip)==null?void 0:B.index)||0)+1,O=x[L];if(O)return{data:{index:L,overflows:A},reset:{placement:O}};let P=(S=A.filter(V=>V.overflows[0]<=0).sort((V,k)=>V.overflows[1]-k.overflows[1])[0])==null?void 0:S.placement;if(!P)switch(f){case"bestFit":{var C;let V=(C=A.map(k=>[k.placement,k.overflows.filter(G=>G>0).reduce((G,ne)=>G+ne,0)]).sort((k,G)=>k[1]-G[1])[0])==null?void 0:C[0];V&&(P=V);break}case"initialPlacement":P=l;break}if(r!==P)return{reset:{placement:P}}}return{}}}};async function rf(e,n){let{placement:t,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=_t(t),l=Mn(t),a=Fn(t)==="y",c=["left","top"].includes(s)?-1:1,u=i&&a?-1:1,p=In(n,e),{mainAxis:d,crossAxis:f,alignmentAxis:m}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...p};return l&&typeof m=="number"&&(f=l==="end"?m*-1:m),a?{x:f*u,y:d*c}:{x:d*c,y:f*u}}var Pi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,o;let{x:r,y:i,placement:s,middlewareData:l}=n,a=await rf(n,e);return s===((t=l.offset)==null?void 0:t.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+a.x,y:i+a.y,data:{...a,placement:s}}}}},Ri=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){let{x:t,y:o,placement:r}=n,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:b=>{let{x:g,y:h}=b;return{x:g,y:h}}},...a}=In(e,n),c={x:t,y:o},u=await Pt(n,a),p=Fn(_t(r)),d=hi(p),f=c[d],m=c[p];if(i){let b=d==="y"?"top":"left",g=d==="y"?"bottom":"right",h=f+u[b],y=f-u[g];f=yi(h,f,y)}if(s){let b=p==="y"?"top":"left",g=p==="y"?"bottom":"right",h=m+u[b],y=m-u[g];m=yi(h,m,y)}let T=l.fn({...n,[d]:f,[p]:m});return{...T,data:{x:T.x-t,y:T.y-o}}}}};var Si=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){let{placement:t,rects:o,platform:r,elements:i}=n,{apply:s=()=>{},...l}=In(e,n),a=await Pt(n,l),c=_t(t),u=Mn(t),p=Fn(t)==="y",{width:d,height:f}=o.floating,m,T;c==="top"||c==="bottom"?(m=c,T=u===(await(r.isRTL==null?void 0:r.isRTL(i.floating))?"start":"end")?"left":"right"):(T=c,m=u==="end"?"top":"bottom");let b=f-a[m],g=d-a[T],h=!n.middlewareData.shift,y=b,v=g;if(p){let R=d-a.left-a.right;v=u||h?Yt(g,R):R}else{let R=f-a.top-a.bottom;y=u||h?Yt(b,R):R}if(h&&!u){let R=We(a.left,0),$=We(a.right,0),A=We(a.top,0),B=We(a.bottom,0);p?v=d-2*(R!==0||$!==0?R+$:We(a.left,a.right)):y=f-2*(A!==0||B!==0?A+B:We(a.top,a.bottom))}await s({...n,availableWidth:v,availableHeight:y});let x=await r.getDimensions(i.floating);return d!==x.width||f!==x.height?{reset:{rects:!0}}:{}}}};function kt(e){return Ul(e)?(e.nodeName||"").toLowerCase():"#document"}function ze(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Rt(e){var n;return(n=(Ul(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Ul(e){return e instanceof Node||e instanceof ze(e).Node}function St(e){return e instanceof Element||e instanceof ze(e).Element}function pt(e){return e instanceof HTMLElement||e instanceof ze(e).HTMLElement}function Gl(e){return typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof ze(e).ShadowRoot}function wn(e){let{overflow:n,overflowX:t,overflowY:o,display:r}=et(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function Vl(e){return["table","td","th"].includes(kt(e))}function dr(e){let n=pr(),t=et(e);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function Wl(e){let n=fn(e);for(;pt(n)&&!So(n);){if(dr(n))return n;n=fn(n)}return null}function pr(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function So(e){return["html","body","#document"].includes(kt(e))}function et(e){return ze(e).getComputedStyle(e)}function Co(e){return St(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function fn(e){if(kt(e)==="html")return e;let n=e.assignedSlot||e.parentNode||Gl(e)&&e.host||Rt(e);return Gl(n)?n.host:n}function jl(e){let n=fn(e);return So(n)?e.ownerDocument?e.ownerDocument.body:e.body:pt(n)&&wn(n)?n:jl(n)}function $t(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);let r=jl(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=ze(r);return i?n.concat(s,s.visualViewport||[],wn(r)?r:[],s.frameElement&&t?$t(s.frameElement):[]):n.concat(r,$t(r,[],t))}function Xl(e){let n=et(e),t=parseFloat(n.width)||0,o=parseFloat(n.height)||0,r=pt(e),i=r?e.offsetWidth:t,s=r?e.offsetHeight:o,l=Po(t)!==i||Po(o)!==s;return l&&(t=i,o=s),{width:t,height:o,$:l}}function Ci(e){return St(e)?e:e.contextElement}function _n(e){let n=Ci(e);if(!pt(n))return wt(1);let t=n.getBoundingClientRect(),{width:o,height:r,$:i}=Xl(n),s=(i?Po(t.width):t.width)/o,l=(i?Po(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}var ff=wt(0);function Yl(e){let n=ze(e);return!pr()||!n.visualViewport?ff:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function df(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==ze(e)?!1:n}function dn(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);let r=e.getBoundingClientRect(),i=Ci(e),s=wt(1);n&&(o?St(o)&&(s=_n(o)):s=_n(e));let l=df(i,t,o)?Yl(i):wt(0),a=(r.left+l.x)/s.x,c=(r.top+l.y)/s.y,u=r.width/s.x,p=r.height/s.y;if(i){let d=ze(i),f=o&&St(o)?ze(o):o,m=d.frameElement;for(;m&&o&&f!==d;){let T=_n(m),b=m.getBoundingClientRect(),g=et(m),h=b.left+(m.clientLeft+parseFloat(g.paddingLeft))*T.x,y=b.top+(m.clientTop+parseFloat(g.paddingTop))*T.y;a*=T.x,c*=T.y,u*=T.x,p*=T.y,a+=h,c+=y,m=ze(m).frameElement}}return cn({width:u,height:p,x:a,y:c})}function pf(e){let{rect:n,offsetParent:t,strategy:o}=e,r=pt(t),i=Rt(t);if(t===i)return n;let s={scrollLeft:0,scrollTop:0},l=wt(1),a=wt(0);if((r||!r&&o!=="fixed")&&((kt(t)!=="body"||wn(i))&&(s=Co(t)),pt(t))){let c=dn(t);l=_n(t),a.x=c.x+t.clientLeft,a.y=c.y+t.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-s.scrollLeft*l.x+a.x,y:n.y*l.y-s.scrollTop*l.y+a.y}}function mf(e){return Array.from(e.getClientRects())}function ql(e){return dn(Rt(e)).left+Co(e).scrollLeft}function Tf(e){let n=Rt(e),t=Co(e),o=e.ownerDocument.body,r=We(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),i=We(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight),s=-t.scrollLeft+ql(e),l=-t.scrollTop;return et(o).direction==="rtl"&&(s+=We(n.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:l}}function bf(e,n){let t=ze(e),o=Rt(e),r=t.visualViewport,i=o.clientWidth,s=o.clientHeight,l=0,a=0;if(r){i=r.width,s=r.height;let c=pr();(!c||c&&n==="fixed")&&(l=r.offsetLeft,a=r.offsetTop)}return{width:i,height:s,x:l,y:a}}function gf(e,n){let t=dn(e,!0,n==="fixed"),o=t.top+e.clientTop,r=t.left+e.clientLeft,i=pt(e)?_n(e):wt(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=r*i.x,c=o*i.y;return{width:s,height:l,x:a,y:c}}function Kl(e,n,t){let o;if(n==="viewport")o=bf(e,t);else if(n==="document")o=Tf(Rt(e));else if(St(n))o=gf(n,t);else{let r=Yl(e);o={...n,x:n.x-r.x,y:n.y-r.y}}return cn(o)}function Jl(e,n){let t=fn(e);return t===n||!St(t)||So(t)?!1:et(t).position==="fixed"||Jl(t,n)}function yf(e,n){let t=n.get(e);if(t)return t;let o=$t(e,[],!1).filter(l=>St(l)&&kt(l)!=="body"),r=null,i=et(e).position==="fixed",s=i?fn(e):e;for(;St(s)&&!So(s);){let l=et(s),a=dr(s);!a&&l.position==="fixed"&&(r=null),(i?!a&&!r:!a&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||wn(s)&&!a&&Jl(e,s))?o=o.filter(u=>u!==s):r=l,s=fn(s)}return n.set(e,o),o}function hf(e){let{element:n,boundary:t,rootBoundary:o,strategy:r}=e,s=[...t==="clippingAncestors"?yf(n,this._c):[].concat(t),o],l=s[0],a=s.reduce((c,u)=>{let p=Kl(n,u,r);return c.top=We(p.top,c.top),c.right=Yt(p.right,c.right),c.bottom=Yt(p.bottom,c.bottom),c.left=We(p.left,c.left),c},Kl(n,l,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function vf(e){return Xl(e)}function Ef(e,n,t){let o=pt(n),r=Rt(n),i=t==="fixed",s=dn(e,!0,i,n),l={scrollLeft:0,scrollTop:0},a=wt(0);if(o||!o&&!i)if((kt(n)!=="body"||wn(r))&&(l=Co(n)),o){let c=dn(n,!0,i,n);a.x=c.x+n.clientLeft,a.y=c.y+n.clientTop}else r&&(a.x=ql(r));return{x:s.left+l.scrollLeft-a.x,y:s.top+l.scrollTop-a.y,width:s.width,height:s.height}}function zl(e,n){return!pt(e)||et(e).position==="fixed"?null:n?n(e):e.offsetParent}function Ql(e,n){let t=ze(e);if(!pt(e))return t;let o=zl(e,n);for(;o&&Vl(o)&&et(o).position==="static";)o=zl(o,n);return o&&(kt(o)==="html"||kt(o)==="body"&&et(o).position==="static"&&!dr(o))?t:o||Wl(e)||t}var xf=async function(e){let{reference:n,floating:t,strategy:o}=e,r=this.getOffsetParent||Ql,i=this.getDimensions;return{reference:Ef(n,await r(t),o),floating:{x:0,y:0,...await i(t)}}};function Pf(e){return et(e).direction==="rtl"}var mr={convertOffsetParentRelativeRectToViewportRelativeRect:pf,getDocumentElement:Rt,getClippingRect:hf,getOffsetParent:Ql,getElementRects:xf,getClientRects:mf,getDimensions:vf,getScale:_n,isElement:St,isRTL:Pf};function Rf(e,n){let t=null,o,r=Rt(e);function i(){clearTimeout(o),t&&t.disconnect(),t=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();let{left:c,top:u,width:p,height:d}=e.getBoundingClientRect();if(l||n(),!p||!d)return;let f=Ro(u),m=Ro(r.clientWidth-(c+p)),T=Ro(r.clientHeight-(u+d)),b=Ro(c),h={rootMargin:-f+"px "+-m+"px "+-T+"px "+-b+"px",threshold:We(0,Yt(1,a))||1},y=!0;function v(x){let R=x[0].intersectionRatio;if(R!==a){if(!y)return s();R?s(!1,R):o=setTimeout(()=>{s(!1,1e-7)},100)}y=!1}try{t=new IntersectionObserver(v,{...h,root:r.ownerDocument})}catch{t=new IntersectionObserver(v,h)}t.observe(e)}return s(!0),i}function Tr(e,n,t,o){o===void 0&&(o={});let{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,c=Ci(e),u=r||i?[...c?$t(c):[],...$t(n)]:[];u.forEach(g=>{r&&g.addEventListener("scroll",t,{passive:!0}),i&&g.addEventListener("resize",t)});let p=c&&l?Rf(c,t):null,d=-1,f=null;s&&(f=new ResizeObserver(g=>{let[h]=g;h&&h.target===c&&f&&(f.unobserve(n),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{f&&f.observe(n)})),t()}),c&&!a&&f.observe(c),f.observe(n));let m,T=a?dn(e):null;a&&b();function b(){let g=dn(e);T&&(g.x!==T.x||g.y!==T.y||g.width!==T.width||g.height!==T.height)&&t(),T=g,m=requestAnimationFrame(b)}return t(),()=>{u.forEach(g=>{r&&g.removeEventListener("scroll",t),i&&g.removeEventListener("resize",t)}),p&&p(),f&&f.disconnect(),f=null,a&&cancelAnimationFrame(m)}}var br=(e,n,t)=>{let o=new Map,r={platform:mr,...t},i={...r.platform,_c:o};return Bl(e,n,{...r,platform:i})};var Ie=le(require("react"),1),hr=require("react"),ta=le(require("react-dom"),1),gr=typeof document!="undefined"?hr.useLayoutEffect:hr.useEffect;function yr(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let t,o,r;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(t=e.length,t!==n.length)return!1;for(o=t;o--!==0;)if(!yr(e[o],n[o]))return!1;return!0}if(r=Object.keys(e),t=r.length,t!==Object.keys(n).length)return!1;for(o=t;o--!==0;)if(!{}.hasOwnProperty.call(n,r[o]))return!1;for(o=t;o--!==0;){let i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!yr(e[i],n[i]))return!1}return!0}return e!==e&&n!==n}function na(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Zl(e,n){let t=na(e);return Math.round(n*t)/t}function ea(e){let n=Ie.useRef(e);return gr(()=>{n.current=e}),n}function oa(e){e===void 0&&(e={});let{placement:n="bottom",strategy:t="absolute",middleware:o=[],platform:r,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:c}=e,[u,p]=Ie.useState({x:0,y:0,strategy:t,placement:n,middlewareData:{},isPositioned:!1}),[d,f]=Ie.useState(o);yr(d,o)||f(o);let[m,T]=Ie.useState(null),[b,g]=Ie.useState(null),h=Ie.useCallback(G=>{G!==R.current&&(R.current=G,T(G))},[]),y=Ie.useCallback(G=>{G!==$.current&&($.current=G,g(G))},[]),v=i||m,x=s||b,R=Ie.useRef(null),$=Ie.useRef(null),A=Ie.useRef(u),B=a!=null,S=ea(a),C=ea(r),L=Ie.useCallback(()=>{if(!R.current||!$.current)return;let G={placement:n,strategy:t,middleware:d};C.current&&(G.platform=C.current),br(R.current,$.current,G).then(ne=>{let F={...ne,isPositioned:!0};O.current&&!yr(A.current,F)&&(A.current=F,ta.flushSync(()=>{p(F)}))})},[d,n,t,C]);gr(()=>{c===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,p(G=>({...G,isPositioned:!1})))},[c]);let O=Ie.useRef(!1);gr(()=>(O.current=!0,()=>{O.current=!1}),[]),gr(()=>{if(v&&(R.current=v),x&&($.current=x),v&&x){if(S.current)return S.current(v,x,L);L()}},[v,x,L,S,B]);let P=Ie.useMemo(()=>({reference:R,floating:$,setReference:h,setFloating:y}),[h,y]),V=Ie.useMemo(()=>({reference:v,floating:x}),[v,x]),k=Ie.useMemo(()=>{let G={position:t,left:0,top:0};if(!V.floating)return G;let ne=Zl(V.floating,u.x),F=Zl(V.floating,u.y);return l?{...G,transform:"translate("+ne+"px, "+F+"px)",...na(V.floating)>=1.5&&{willChange:"transform"}}:{position:t,left:ne,top:F}},[t,l,V.floating,u.x,u.y]);return Ie.useMemo(()=>({...u,update:L,refs:P,elements:V,floatingStyles:k}),[u,L,P,V,k])}var Ao=(e,n)=>({...Pi(e),options:[e,n]}),Ai=(e,n)=>({...Ri(e),options:[e,n]});var Oi=(e,n)=>({...xi(e),options:[e,n]}),Di=(e,n)=>({...Si(e),options:[e,n]});var $n=require("react-dom");var ua={...te},Sf=ua.useInsertionEffect,Cf=Sf||(e=>e());function ca(e){let n=te.useRef(()=>{});return Cf(()=>{n.current=e}),te.useCallback(function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return n.current==null?void 0:n.current(...o)},[])}var Af="ArrowUp",Of="ArrowDown",Df="ArrowLeft",Lf="ArrowRight";var Ii=typeof document!="undefined"?Oo.useLayoutEffect:Oo.useEffect;var If=[Df,Lf],Mf=[Af,Of],qh=[...If,...Mf];var ra=!1,Ff=0,ia=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Ff++;function wf(){let[e,n]=te.useState(()=>ra?ia():void 0);return Ii(()=>{e==null&&n(ia())},[]),te.useEffect(()=>{ra=!0},[]),e}var _f=ua.useId,$f=_f||wf;function kf(){let e=new Map;return{emit(n,t){var o;(o=e.get(n))==null||o.forEach(r=>r(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var o;e.set(n,((o=e.get(n))==null?void 0:o.filter(r=>r!==t))||[])}}}var Hf=te.createContext(null),Nf=te.createContext(null),Bf=()=>{var e;return((e=te.useContext(Hf))==null?void 0:e.id)||null},Gf=()=>te.useContext(Nf);function Uf(e){let{open:n=!1,onOpenChange:t,elements:o}=e,r=$f(),i=te.useRef({}),[s]=te.useState(()=>kf()),l=Bf()!=null,[a,c]=te.useState(o.reference),u=ca((f,m,T)=>{i.current.openEvent=f?m:void 0,s.emit("openchange",{open:f,event:m,reason:T,nested:l}),t==null||t(f,m,T)}),p=te.useMemo(()=>({setPositionReference:c}),[]),d=te.useMemo(()=>({reference:a||o.reference||null,floating:o.floating||null,domReference:o.reference}),[a,o.reference,o.floating]);return te.useMemo(()=>({dataRef:i,open:n,onOpenChange:u,elements:d,events:s,floatingId:r,refs:p}),[n,u,d,s,r,p])}function fa(e){e===void 0&&(e={});let{nodeId:n}=e,t=Uf({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||t,r=o.elements,[i,s]=te.useState(null),[l,a]=te.useState(null),u=(r==null?void 0:r.reference)||i,p=te.useRef(null),d=Gf();Ii(()=>{u&&(p.current=u)},[u]);let f=oa({...e,elements:{...r,...l&&{reference:l}}}),m=te.useCallback(y=>{let v=Eo(y)?{getBoundingClientRect:()=>y.getBoundingClientRect(),contextElement:y}:y;a(v),f.refs.setReference(v)},[f.refs]),T=te.useCallback(y=>{(Eo(y)||y===null)&&(p.current=y,s(y)),(Eo(f.refs.reference.current)||f.refs.reference.current===null||y!==null&&!Eo(y))&&f.refs.setReference(y)},[f.refs]),b=te.useMemo(()=>({...f.refs,setReference:T,setPositionReference:m,domReference:p}),[f.refs,T,m]),g=te.useMemo(()=>({...f.elements,domReference:u}),[f.elements,u]),h=te.useMemo(()=>({...f,...o,refs:b,elements:g,nodeId:n}),[f,b,g,n,o]);return Ii(()=>{o.dataRef.current.floatingContext=h;let y=d==null?void 0:d.nodesRef.current.find(v=>v.id===n);y&&(y.context=h)}),te.useMemo(()=>({...f,context:h,refs:b,elements:g}),[f,b,g,h])}var sa="active",la="selected";function Li(e,n,t){let o=new Map,r=t==="item",i=e;if(r&&e){let{[sa]:s,[la]:l,...a}=e;i=a}return{...t==="floating"&&{tabIndex:-1},...i,...n.map(s=>{let l=s?s[t]:null;return typeof l=="function"?e?l(e):null:l}).concat(e).reduce((s,l)=>(l&&Object.entries(l).forEach(a=>{let[c,u]=a;if(!(r&&[sa,la].includes(c)))if(c.indexOf("on")===0){if(o.has(c)||o.set(c,[]),typeof u=="function"){var p;(p=o.get(c))==null||p.push(u),s[c]=function(){for(var d,f=arguments.length,m=new Array(f),T=0;T<f;T++)m[T]=arguments[T];return(d=o.get(c))==null?void 0:d.map(b=>b(...m)).find(b=>b!==void 0)}}}else s[c]=u}),s),{})}}function da(e){e===void 0&&(e=[]);let n=e,t=te.useCallback(i=>Li(i,e,"reference"),n),o=te.useCallback(i=>Li(i,e,"floating"),n),r=te.useCallback(i=>Li(i,e,"item"),e.map(i=>i==null?void 0:i.item));return te.useMemo(()=>({getReferenceProps:t,getFloatingProps:o,getItemProps:r}),[t,o,r])}function aa(e,n){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:n}}}}var pa=e=>({name:"inner",options:e,async fn(n){let{listRef:t,overflowRef:o,onFallbackChange:r,offset:i=0,index:s=0,minItemsVisible:l=4,referenceOverflowThreshold:a=0,scrollRef:c,...u}=e,{rects:p,elements:{floating:d}}=n,f=t.current[s];if(!f)return{};let m={...n,...await Ao(-f.offsetTop-d.clientTop-p.reference.height/2-f.offsetHeight/2-i).fn(n)},T=(c==null?void 0:c.current)||d,b=await Pt(aa(m,T.scrollHeight),u),g=await Pt(m,{...u,elementContext:"reference"}),h=Math.max(0,b.top),y=m.y+h,v=Math.max(0,T.scrollHeight-h-Math.max(0,b.bottom));return T.style.maxHeight=v+"px",T.scrollTop=h,r&&(T.offsetHeight<f.offsetHeight*Math.min(l,t.current.length-1)-1||g.top>=-a||g.bottom>=-a?(0,$n.flushSync)(()=>r(!0)):(0,$n.flushSync)(()=>r(!1))),o&&(o.current=await Pt(aa({...m,y},T.offsetHeight),u)),{y}}});function ma(e,n){let{open:t,elements:o}=e,{enabled:r=!0,overflowRef:i,scrollRef:s,onChange:l}=n,a=ca(l),c=te.useRef(!1),u=te.useRef(null),p=te.useRef(null);return te.useEffect(()=>{if(!r)return;function d(m){if(m.ctrlKey||!f||i.current==null)return;let T=m.deltaY,b=i.current.top>=-.5,g=i.current.bottom>=-.5,h=f.scrollHeight-f.clientHeight,y=T<0?-1:1,v=T<0?"max":"min";f.scrollHeight<=f.clientHeight||(!b&&T>0||!g&&T<0?(m.preventDefault(),(0,$n.flushSync)(()=>{a(x=>x+Math[v](T,h*y))})):/firefox/i.test(wl())&&(f.scrollTop+=T))}let f=(s==null?void 0:s.current)||o.floating;if(t&&f)return f.addEventListener("wheel",d),requestAnimationFrame(()=>{u.current=f.scrollTop,i.current!=null&&(p.current={...i.current})}),()=>{u.current=null,p.current=null,f.removeEventListener("wheel",d)}},[r,t,o.floating,i,s,a]),te.useMemo(()=>r?{floating:{onKeyDown(){c.current=!0},onWheel(){c.current=!1},onPointerMove(){c.current=!1},onScroll(){let d=(s==null?void 0:s.current)||o.floating;if(!(!i.current||!d||!c.current)){if(u.current!==null){let f=d.scrollTop-u.current;(i.current.bottom<-.5&&f<-1||i.current.top<-.5&&f>1)&&(0,$n.flushSync)(()=>a(m=>m+f))}requestAnimationFrame(()=>{u.current=d.scrollTop})}}}}:{},[r,i,o.floating,s,a])}var Fi=le(require("react"),1),Ee=require("react");var kn=(0,Ee.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});kn.displayName="FloatingContext";var wi=(0,Ee.createContext)(null);wi.displayName="PlacementContext";function qt(e){return(0,Ee.useMemo)(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function Jt(){return(0,Ee.useContext)(kn).setReference}function vr(){return(0,Ee.useContext)(kn).getReferenceProps}function Qt(){let{getFloatingProps:e,slot:n}=(0,Ee.useContext)(kn);return(0,Ee.useCallback)((...t)=>Object.assign({},e(...t),{"data-anchor":n.anchor}),[e,n])}function Zt(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let n=(0,Ee.useContext)(wi),t=(0,Ee.useMemo)(()=>e,[JSON.stringify(e,(r,i)=>{var s;return(s=i==null?void 0:i.outerHTML)!=null?s:i})]);j(()=>{n==null||n(t!=null?t:null)},[n,t]);let o=(0,Ee.useContext)(kn);return(0,Ee.useMemo)(()=>[o.setFloating,e?o.styles:{}],[o.setFloating,e,o.styles])}var Ta=4;function en({children:e,enabled:n=!0}){let[t,o]=(0,Ee.useState)(null),[r,i]=(0,Ee.useState)(0),s=(0,Ee.useRef)(null),[l,a]=(0,Ee.useState)(null);Vf(l);let c=n&&t!==null&&l!==null,{to:u="bottom",gap:p=0,offset:d=0,padding:f=0,inner:m}=Wf(t,l),[T,b="center"]=u.split(" ");j(()=>{c&&i(0)},[c]);let{refs:g,floatingStyles:h,context:y}=fa({open:c,placement:T==="selection"?b==="center"?"bottom":`bottom-${b}`:b==="center"?`${T}`:`${T}-${b}`,strategy:"absolute",transform:!1,middleware:[Ao({mainAxis:T==="selection"?0:p,crossAxis:d}),Ai({padding:f}),T!=="selection"&&Oi({padding:f}),T==="selection"&&m?pa({...m,padding:f,overflowRef:s,offset:r,minItemsVisible:Ta,referenceOverflowThreshold:f,onFallbackChange(C){var G,ne;if(!C)return;let L=y.elements.floating;if(!L)return;let O=parseFloat(getComputedStyle(L).scrollPaddingBottom)||0,P=Math.min(Ta,L.childElementCount),V=0,k=0;for(let F of(ne=(G=y.elements.floating)==null?void 0:G.childNodes)!=null?ne:[])if(Te(F)){let I=F.offsetTop,U=I+F.clientHeight+O,W=L.scrollTop,Y=W+L.clientHeight;if(I>=W&&U<=Y)P--;else{k=Math.max(0,Math.min(U,Y)-Math.max(I,W)),V=F.clientHeight;break}}P>=1&&i(F=>{let I=V*P-k+O;return F>=I?F:I})}}):null,Di({padding:f,apply({availableWidth:C,availableHeight:L,elements:O}){Object.assign(O.floating.style,{overflow:"auto",maxWidth:`${C}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${L}px)`})}})].filter(Boolean),whileElementsMounted:Tr}),[v=T,x=b]=y.placement.split("-");T==="selection"&&(v="selection");let R=(0,Ee.useMemo)(()=>({anchor:[v,x].filter(Boolean).join(" ")}),[v,x]),$=ma(y,{overflowRef:s,onChange:i}),{getReferenceProps:A,getFloatingProps:B}=da([$]),S=E(C=>{a(C),g.setFloating(C)});return Fi.createElement(wi.Provider,{value:o},Fi.createElement(kn.Provider,{value:{setFloating:S,setReference:g.setReference,styles:h,getReferenceProps:A,getFloatingProps:B,slot:R}},e))}function Vf(e){j(()=>{if(!e)return;let n=new MutationObserver(()=>{let t=window.getComputedStyle(e).maxHeight,o=parseFloat(t);if(isNaN(o))return;let r=parseInt(t);isNaN(r)||o!==r&&(e.style.maxHeight=`${Math.ceil(o)}px`)});return n.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{n.disconnect()}},[e])}function Wf(e,n){var i,s,l;let t=Mi((i=e==null?void 0:e.gap)!=null?i:"var(--anchor-gap, 0)",n),o=Mi((s=e==null?void 0:e.offset)!=null?s:"var(--anchor-offset, 0)",n),r=Mi((l=e==null?void 0:e.padding)!=null?l:"var(--anchor-padding, 0)",n);return{...e,gap:t,offset:o,padding:r}}function Mi(e,n,t=void 0){let o=Se(),r=E((a,c)=>{if(a==null)return[t,null];if(typeof a=="number")return[a,null];if(typeof a=="string"){if(!c)return[t,null];let u=ba(a,c);return[u,p=>{let d=ga(a);{let f=d.map(m=>window.getComputedStyle(c).getPropertyValue(m));o.requestAnimationFrame(function m(){o.nextFrame(m);let T=!1;for(let[g,h]of d.entries()){let y=window.getComputedStyle(c).getPropertyValue(h);if(f[g]!==y){f[g]=y,T=!0;break}}if(!T)return;let b=ba(a,c);u!==b&&(p(b),u=b)})}return o.dispose}]}return[t,null]}),i=(0,Ee.useMemo)(()=>r(e,n)[0],[e,n]),[s=i,l]=(0,Ee.useState)();return j(()=>{let[a,c]=r(e,n);if(l(a),!!c)return c(l)},[e,n]),s}function ga(e){let n=/var\((.*)\)/.exec(e);if(n){let t=n[1].indexOf(",");if(t===-1)return[n[1]];let o=n[1].slice(0,t).trim(),r=n[1].slice(t+1).trim();return r?[o,...ga(r)]:[o]}return[]}function ba(e,n){let t=document.createElement("div");n.appendChild(t),t.style.setProperty("margin-top","0px","important"),t.style.setProperty("margin-top",e,"important");let o=parseFloat(window.getComputedStyle(t).marginTop)||0;return n.removeChild(t),o}var Do=le(require("react"),1);function ya({children:e,freeze:n}){let t=Hn(n,e);return Do.default.createElement(Do.default.Fragment,null,t)}function Hn(e,n){let[t,o]=(0,Do.useState)(n);return!e&&t!==n&&o(n),e?t:n}var pn=le(require("react"),1),Er=(0,pn.createContext)(null);Er.displayName="OpenClosedContext";function He(){return(0,pn.useContext)(Er)}function rt({value:e,children:n}){return pn.default.createElement(Er.Provider,{value:e},n)}function Nn({children:e}){return pn.default.createElement(Er.Provider,{value:null},e)}function ha(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",n),n())}var it=[];ha(()=>{function e(n){if(!Le(n.target)||n.target===document.body||it[0]===n.target)return;let t=n.target;t=t.closest(bo),it.unshift(t!=null?t:n.target),it=it.filter(o=>o!=null&&o.isConnected),it.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function jf(e){throw new Error("Unexpected object: "+e)}function tt(e,n){let t=n.resolveItems();if(t.length<=0)return null;let o=n.resolveActiveIndex(),r=o!=null?o:-1;switch(e.focus){case 0:{for(let i=0;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 1:{r===-1&&(r=t.length);for(let i=r-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 2:{for(let i=r+1;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 3:{for(let i=t.length-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 4:{for(let i=0;i<t.length;++i)if(n.resolveId(t[i],i,t)===e.id)return i;return o}case 5:return null;default:jf(e)}}var ye=le(require("react"),1),xa=require("react-dom");var xr=require("react");function st(e){let n=E(e),t=(0,xr.useRef)(!1);(0,xr.useEffect)(()=>(t.current=!1,()=>{t.current=!0,Dt(()=>{t.current&&n()})}),[n])}var mn=le(require("react"),1);function Kf(){let e=typeof document=="undefined";return"useSyncExternalStore"in mn?(o=>o.useSyncExternalStore)(mn)(()=>()=>{},()=>!1,()=>!e):!1}function Ht(){let e=Kf(),[n,t]=mn.useState(Ue.isHandoffComplete);return n&&Ue.isHandoffComplete===!1&&t(!1),mn.useEffect(()=>{n!==!0&&t(!0)},[n]),mn.useEffect(()=>Ue.handoff(),[]),e?!1:n}var Bn=le(require("react"),1),va=(0,Bn.createContext)(!1);function Ea(){return(0,Bn.useContext)(va)}function _i(e){return Bn.default.createElement(va.Provider,{value:e.force},e.children)}function zf(e){let n=Ea(),t=(0,ye.useContext)(Ra),[o,r]=(0,ye.useState)(()=>{var l;if(!n&&t!==null)return(l=t.current)!=null?l:null;if(Ue.isServer)return null;let i=e==null?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(e===null)return null;let s=e.createElement("div");return s.setAttribute("id","headlessui-portal-root"),e.body.appendChild(s)});return(0,ye.useEffect)(()=>{o!==null&&(e!=null&&e.body.contains(o)||e==null||e.body.appendChild(o))},[o,e]),(0,ye.useEffect)(()=>{n||t!==null&&r(t.current)},[t,r,n]),o}var Pa=ye.Fragment,Xf=_(function(n,t){let{ownerDocument:o=null,...r}=n,i=(0,ye.useRef)(null),s=z(Pn(T=>{i.current=T}),t),l=Re(i),a=o!=null?o:l,c=zf(a),[u]=(0,ye.useState)(()=>{var T;return Ue.isServer?null:(T=a==null?void 0:a.createElement("div"))!=null?T:null}),p=(0,ye.useContext)($i),d=Ht();j(()=>{!c||!u||c.contains(u)||(u.setAttribute("data-headlessui-portal",""),c.appendChild(u))},[c,u]),j(()=>{if(u&&p)return p.register(u)},[p,u]),st(()=>{var T;!c||!u||(fo(u)&&c.contains(u)&&c.removeChild(u),c.childNodes.length<=0&&((T=c.parentElement)==null||T.removeChild(c)))});let f=N();return d?!c||!u?null:(0,xa.createPortal)(f({ourProps:{ref:s},theirProps:r,slot:{},defaultTag:Pa,name:"Portal"}),u):null});function Yf(e,n){let t=z(n),{enabled:o=!0,ownerDocument:r,...i}=e,s=N();return o?ye.default.createElement(Xf,{...i,ownerDocument:r,ref:t}):s({ourProps:{ref:t},theirProps:i,slot:{},defaultTag:Pa,name:"Portal"})}var qf=ye.Fragment,Ra=(0,ye.createContext)(null);function Jf(e,n){let{target:t,...o}=e,i={ref:z(n)},s=N();return ye.default.createElement(Ra.Provider,{value:t},s({ourProps:i,theirProps:o,defaultTag:qf,name:"Popover.Group"}))}var $i=(0,ye.createContext)(null);function Pr(){let e=(0,ye.useContext)($i),n=(0,ye.useRef)([]),t=E(i=>(n.current.push(i),e&&e.register(i),()=>o(i))),o=E(i=>{let s=n.current.indexOf(i);s!==-1&&n.current.splice(s,1),e&&e.unregister(i)}),r=(0,ye.useMemo)(()=>({register:t,unregister:o,portals:n}),[t,o,n]);return[n,(0,ye.useMemo)(()=>function({children:s}){return ye.default.createElement($i.Provider,{value:r},s)},[r])]}var Qf=_(Yf),ki=_(Jf),lt=Object.assign(Qf,{Group:ki});function Hi(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=n(e.options.slice()),r=o.length>0&&o[0].dataRef.current.order!==null?o.sort((s,l)=>s.dataRef.current.order-l.dataRef.current.order):Ve(o,s=>s.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}var Zf={[1](e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let o=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(o!==-1)return{...e,activeOptionIndex:o,comboboxState:0,__demoMode:!1}}return{...e,comboboxState:0,__demoMode:!1}},[3](e,n){return e.isTyping===n.isTyping?e:{...e,isTyping:n.isTyping}},[2](e,n){var i,s,l,a;if((i=e.dataRef.current)!=null&&i.disabled||e.optionsElement&&!((s=e.dataRef.current)!=null&&s.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let{options:c,disabled:u}=e.virtual,p=n.focus===4?n.idx:tt(n,{resolveItems:()=>c,resolveActiveIndex:()=>{var f,m;return(m=(f=e.activeOptionIndex)!=null?f:c.findIndex(T=>!u(T)))!=null?m:null},resolveDisabled:u,resolveId(){throw new Error("Function not implemented.")}}),d=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===p&&e.activationTrigger===d?e:{...e,activeOptionIndex:p,activationTrigger:d,isTyping:!1,__demoMode:!1}}let t=Hi(e);if(t.activeOptionIndex===null){let c=t.options.findIndex(u=>!u.dataRef.current.disabled);c!==-1&&(t.activeOptionIndex=c)}let o=n.focus===4?n.idx:tt(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled}),r=(a=n.trigger)!=null?a:2;return e.activeOptionIndex===o&&e.activationTrigger===r?e:{...e,...t,isTyping:!1,activeOptionIndex:o,activationTrigger:r,__demoMode:!1}},[4]:(e,n)=>{var i,s,l,a;if((i=e.dataRef.current)!=null&&i.virtual)return{...e,options:[...e.options,n.payload]};let t=n.payload,o=Hi(e,c=>(c.push(t),c));e.activeOptionIndex===null&&(l=(s=e.dataRef.current).isSelected)!=null&&l.call(s,n.payload.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(t));let r={...e,...o,activationTrigger:2};return(a=e.dataRef.current)!=null&&a.__demoMode&&e.dataRef.current.value===void 0&&(r.activeOptionIndex=0),r},[5]:(e,n)=>{var o;if((o=e.dataRef.current)!=null&&o.virtual)return{...e,options:e.options.filter(r=>r.id!==n.id)};let t=Hi(e,r=>{let i=r.findIndex(s=>s.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:2}},[6]:(e,n)=>e.defaultToFirstOption===n.value?e:{...e,defaultToFirstOption:n.value},[7]:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},[8]:(e,n)=>{var o,r;if(e.virtual===null)return{...e,virtual:{options:n.options,disabled:(o=n.disabled)!=null?o:()=>!1}};if(e.virtual.options===n.options&&e.virtual.disabled===n.disabled)return e;let t=e.activeOptionIndex;if(e.activeOptionIndex!==null){let i=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);i!==-1?t=i:t=null}return{...e,activeOptionIndex:t,virtual:{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}}},[9]:(e,n)=>e.inputElement===n.element?e:{...e,inputElement:n.element},[10]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[11]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element}},Gn=class extends ot{constructor(t){super(t);Oe(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return q(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(c=>r(c,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:(t,o)=>(this.send({type:4,payload:{id:t,dataRef:o}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(o.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:t})}),goToOption:(t,o)=>(this.send({type:6,value:!1}),this.send({type:2,...t,trigger:o})),setIsTyping:t=>{this.send({type:3,isTyping:t})},closeCombobox:()=>{var t,o;this.send({type:1}),this.send({type:6,value:!1}),(o=(t=this.state.dataRef.current).onClose)==null||o.call(t)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:t=>{this.send({type:7,trigger:t})},selectActiveOption:()=>{let t=this.selectors.activeOptionIndex(this.state);if(t!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[t]);else{let{dataRef:o}=this.state.options[t];this.actions.onChange(o.current.value)}this.actions.goToOption({focus:4,idx:t})}},setInputElement:t=>{this.send({type:9,element:t})},setButtonElement:t=>{this.send({type:10,element:t})},setOptionsElement:t=>{this.send({type:11,element:t})}});Oe(this,"selectors",{activeDescendantId:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);if(o!==null)return t.virtual?(i=t.options.find(s=>!s.dataRef.current.disabled&&t.dataRef.current.compare(s.dataRef.current.value,t.virtual.options[o])))==null?void 0:i.id:(r=t.options[o])==null?void 0:r.id},activeOptionIndex:t=>{if(t.defaultToFirstOption&&t.activeOptionIndex===null&&(t.virtual?t.virtual.options.length>0:t.options.length>0)){if(t.virtual){let{options:r,disabled:i}=t.virtual,s=r.findIndex(l=>{var a;return!((a=i==null?void 0:i(l))!=null&&a)});if(s!==-1)return s}let o=t.options.findIndex(r=>!r.dataRef.current.disabled);if(o!==-1)return o}return t.activeOptionIndex},activeOption:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);return o===null?null:t.virtual?t.virtual.options[o!=null?o:0]:(i=(r=t.options[o])==null?void 0:r.dataRef.current.value)!=null?i:null},isActive:(t,o,r)=>{var s;let i=this.selectors.activeOptionIndex(t);return i===null?!1:t.virtual?i===t.dataRef.current.calculateIndex(o):((s=t.options[i])==null?void 0:s.id)===r},shouldScrollIntoView:(t,o,r)=>!(t.virtual||t.__demoMode||t.comboboxState!==0||t.activationTrigger===0||!this.selectors.isActive(t,o,r))});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,virtual:o=null,__demoMode:r=!1}){var i;return new Gn({id:t,dataRef:{current:{}},comboboxState:r?0:1,isTyping:!1,options:[],virtual:o?{options:o.options,disabled:(i=o.disabled)!=null?i:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:r})}reduce(t,o){return q(o.type,Zf,t,o)}};var Un=require("react");var Ni=(0,Un.createContext)(null);function Vn(e){let n=(0,Un.useContext)(Ni);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Bi),t}return n}function Bi({id:e,virtual:n=null,__demoMode:t=!1}){let o=(0,Un.useMemo)(()=>Gn.new({id:e,virtual:n,__demoMode:t}),[]);return st(()=>o.dispose()),o}var Lo=(0,Z.createContext)(null);Lo.displayName="ComboboxDataContext";function jn(e){let n=(0,Z.useContext)(Lo);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,jn),t}return n}var Aa=(0,Z.createContext)(null);function ed(e){let n=Vn("VirtualProvider"),t=jn("VirtualProvider"),{options:o}=t.virtual,r=ee(n,f=>f.optionsElement),[i,s]=(0,Z.useMemo)(()=>{let f=r;if(!f)return[0,0];let m=window.getComputedStyle(f);return[parseFloat(m.paddingBlockStart||m.paddingTop),parseFloat(m.paddingBlockEnd||m.paddingBottom)]},[r]),l=sl({enabled:o.length!==0,scrollPaddingStart:i,scrollPaddingEnd:s,count:o.length,estimateSize(){return 40},getScrollElement(){return n.state.optionsElement},overscan:12}),[a,c]=(0,Z.useState)(0);j(()=>{c(f=>f+1)},[o]);let u=l.getVirtualItems(),p=ee(n,f=>f.activationTrigger===0),d=ee(n,n.selectors.activeOptionIndex);return u.length===0?null:Z.default.createElement(Aa.Provider,{value:l},Z.default.createElement("div",{style:{position:"relative",width:"100%",height:`${l.getTotalSize()}px`},ref:f=>{f&&(p||d!==null&&o.length>d&&l.scrollToIndex(d))}},u.map(f=>{var m;return Z.default.createElement(Z.Fragment,{key:f.key},Z.default.cloneElement((m=e.children)==null?void 0:m.call(e,{...e.slot,option:o[f.index]}),{key:`${a}-${f.key}`,"data-index":f.index,"aria-setsize":o.length,"aria-posinset":f.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${f.start}px)`,overflowAnchor:"none"}}))})))}var td=Z.Fragment;function nd(e,n){let t=(0,J.useId)(),o=ge(),{value:r,defaultValue:i,onChange:s,form:l,name:a,by:c,invalid:u=!1,disabled:p=o||!1,onClose:d,__demoMode:f=!1,multiple:m=!1,immediate:T=!1,virtual:b=null,nullable:g,...h}=e,y=gt(i),[v=m?[]:void 0,x]=bt(r,s,y),R=Bi({id:t,virtual:b,__demoMode:f}),$=(0,Z.useRef)({static:!1,hold:!1}),A=Sn(c),B=E(D=>b?c===null?b.options.indexOf(D):b.options.findIndex(X=>A(X,D)):R.state.options.findIndex(X=>A(X.dataRef.current.value,D))),S=(0,Z.useCallback)(D=>q(O.mode,{[1]:()=>v.some(X=>A(X,D)),[0]:()=>A(v,D)}),[v]),C=ee(R,D=>D.virtual),L=E(()=>d==null?void 0:d()),O=(0,Z.useMemo)(()=>({__demoMode:f,immediate:T,optionsPropsRef:$,value:v,defaultValue:y,disabled:p,invalid:u,mode:m?1:0,virtual:b?C:null,onChange:x,isSelected:S,calculateIndex:B,compare:A,onClose:L}),[v,y,p,u,m,x,S,f,R,b,C,L]);j(()=>{var D;b&&R.send({type:8,options:b.options,disabled:(D=b.disabled)!=null?D:null})},[b,b==null?void 0:b.options,b==null?void 0:b.disabled]),j(()=>{R.state.dataRef.current=O},[O]);let[P,V,k,G]=ee(R,D=>[D.comboboxState,D.buttonElement,D.inputElement,D.optionsElement]),ne=$e.get(null),F=ee(ne,(0,Z.useCallback)(D=>ne.selectors.isTop(D,t),[ne,t]));Et(F,[V,k,G],()=>R.actions.closeCombobox());let I=ee(R,R.selectors.activeOptionIndex),U=ee(R,R.selectors.activeOption),W=(0,Z.useMemo)(()=>({open:P===0,disabled:p,invalid:u,activeIndex:I,activeOption:U,value:v}),[O,p,v,u,U,P]),[Y,K]=Be(),w=n===null?{}:{ref:n},H=(0,Z.useCallback)(()=>{if(y!==void 0)return x==null?void 0:x(y)},[x,y]),M=N();return Z.default.createElement(K,{value:Y,props:{htmlFor:k==null?void 0:k.id},slot:{open:P===0,disabled:p}},Z.default.createElement(en,null,Z.default.createElement(Lo.Provider,{value:O},Z.default.createElement(Ni.Provider,{value:R},Z.default.createElement(rt,{value:q(P,{[0]:1,[1]:2})},a!=null&&Z.default.createElement(yt,{disabled:p,data:v!=null?{[a]:v}:{},form:l,onReset:H}),M({ourProps:w,theirProps:h,slot:W,defaultTag:td,name:"Combobox"}))))))}var od="input";function rd(e,n){var Y,K;let t=Vn("Combobox.Input"),o=jn("Combobox.Input"),r=(0,J.useId)(),i=we(),{id:s=i||`headlessui-combobox-input-${r}`,onChange:l,displayValue:a,disabled:c=o.disabled||!1,autoFocus:u=!1,type:p="text",...d}=e,[f]=ee(t,w=>[w.inputElement]),m=(0,Z.useRef)(null),T=z(m,n,Jt(),t.actions.setInputElement),b=Re(f),[g,h]=ee(t,w=>[w.comboboxState,w.isTyping]),y=Se(),v=E(()=>{t.actions.onChange(null),t.state.optionsElement&&(t.state.optionsElement.scrollTop=0),t.actions.goToOption({focus:5})}),x=(0,Z.useMemo)(()=>{var w;return typeof a=="function"&&o.value!==void 0?(w=a(o.value))!=null?w:"":typeof o.value=="string"?o.value:""},[o.value,a]);un(([w,H],[M,D])=>{if(t.state.isTyping)return;let X=m.current;X&&((D===0&&H===1||w!==M)&&(X.value=w),requestAnimationFrame(()=>{if(t.state.isTyping||!X||(b==null?void 0:b.activeElement)!==X)return;let{selectionStart:re,selectionEnd:je}=X;Math.abs((je!=null?je:0)-(re!=null?re:0))===0&&re===0&&X.setSelectionRange(X.value.length,X.value.length)}))},[x,g,b,h]),un(([w],[H])=>{if(w===0&&H===1){if(t.state.isTyping)return;let M=m.current;if(!M)return;let D=M.value,{selectionStart:X,selectionEnd:re,selectionDirection:je}=M;M.value="",M.value=D,je!==null?M.setSelectionRange(X,re,je):M.setSelectionRange(X,re)}},[g]);let R=(0,Z.useRef)(!1),$=E(()=>{R.current=!0}),A=E(()=>{y.nextFrame(()=>{R.current=!1})}),B=E(w=>{switch(t.actions.setIsTyping(!0),w.key){case"Enter":if(t.state.comboboxState!==0||R.current)return;if(w.preventDefault(),w.stopPropagation(),t.selectors.activeOptionIndex(t.state)===null){t.actions.closeCombobox();return}t.actions.selectActiveOption(),o.mode===0&&t.actions.closeCombobox();break;case"ArrowDown":return w.preventDefault(),w.stopPropagation(),q(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:2}),[1]:()=>t.actions.openCombobox()});case"ArrowUp":return w.preventDefault(),w.stopPropagation(),q(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:1}),[1]:()=>{(0,Tn.flushSync)(()=>t.actions.openCombobox()),o.value||t.actions.goToOption({focus:3})}});case"Home":if(w.shiftKey)break;return w.preventDefault(),w.stopPropagation(),t.actions.goToOption({focus:0});case"PageUp":return w.preventDefault(),w.stopPropagation(),t.actions.goToOption({focus:0});case"End":if(w.shiftKey)break;return w.preventDefault(),w.stopPropagation(),t.actions.goToOption({focus:3});case"PageDown":return w.preventDefault(),w.stopPropagation(),t.actions.goToOption({focus:3});case"Escape":return t.state.comboboxState!==0?void 0:(w.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&w.stopPropagation(),o.mode===0&&o.value===null&&v(),t.actions.closeCombobox());case"Tab":if(t.state.comboboxState!==0)return;o.mode===0&&t.state.activationTrigger!==1&&t.actions.selectActiveOption(),t.actions.closeCombobox();break}}),S=E(w=>{l==null||l(w),o.mode===0&&w.target.value===""&&v(),t.actions.openCombobox()}),C=E(w=>{var M,D,X;let H=(M=w.relatedTarget)!=null?M:it.find(re=>re!==w.currentTarget);if(!((D=t.state.optionsElement)!=null&&D.contains(H))&&!((X=t.state.buttonElement)!=null&&X.contains(H))&&t.state.comboboxState===0)return w.preventDefault(),o.mode===0&&o.value===null&&v(),t.actions.closeCombobox()}),L=E(w=>{var M,D,X;let H=(M=w.relatedTarget)!=null?M:it.find(re=>re!==w.currentTarget);(D=t.state.buttonElement)!=null&&D.contains(H)||(X=t.state.optionsElement)!=null&&X.contains(H)||o.disabled||o.immediate&&t.state.comboboxState!==0&&y.microTask(()=>{(0,Tn.flushSync)(()=>t.actions.openCombobox()),t.actions.setActivationTrigger(1)})}),O=Fe(),P=Ne(),{isFocused:V,focusProps:k}=ce({autoFocus:u}),{isHovered:G,hoverProps:ne}=fe({isDisabled:c}),F=ee(t,w=>w.optionsElement),I=(0,Z.useMemo)(()=>({open:g===0,disabled:c,invalid:o.invalid,hover:G,focus:V,autofocus:u}),[o,G,V,u,c,o.invalid]),U=ae({ref:T,id:s,role:"combobox",type:p,"aria-controls":F==null?void 0:F.id,"aria-expanded":g===0,"aria-activedescendant":ee(t,t.selectors.activeDescendantId),"aria-labelledby":O,"aria-describedby":P,"aria-autocomplete":"list",defaultValue:(K=(Y=e.defaultValue)!=null?Y:o.defaultValue!==void 0?a==null?void 0:a(o.defaultValue):null)!=null?K:o.defaultValue,disabled:c||void 0,autoFocus:u,onCompositionStart:$,onCompositionEnd:A,onKeyDown:B,onChange:S,onFocus:L,onBlur:C},k,ne);return N()({ourProps:U,theirProps:d,slot:I,defaultTag:od,name:"Combobox.Input"})}var id="button";function sd(e,n){let t=Vn("Combobox.Button"),o=jn("Combobox.Button"),[r,i]=(0,Z.useState)(null),s=z(n,i,t.actions.setButtonElement),l=(0,J.useId)(),{id:a=`headlessui-combobox-button-${l}`,disabled:c=o.disabled||!1,autoFocus:u=!1,...p}=e,[d,f,m]=ee(t,O=>[O.comboboxState,O.inputElement,O.optionsElement]),T=gi(f),b=d===0;Dn(b,{trigger:r,action:(0,Z.useCallback)(O=>{if(r!=null&&r.contains(O.target))return Ge.Ignore;if(f!=null&&f.contains(O.target))return Ge.Ignore;let P=O.target.closest('[role="option"]:not([data-disabled])');return Te(P)?Ge.Select(P):m!=null&&m.contains(O.target)?Ge.Ignore:Ge.Close},[r,f,m]),close:t.actions.closeCombobox,select:t.actions.selectActiveOption});let g=E(O=>{switch(O.key){case" ":case"Enter":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&(0,Tn.flushSync)(()=>t.actions.openCombobox()),T();return;case"ArrowDown":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&((0,Tn.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:0})),T();return;case"ArrowUp":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&((0,Tn.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:3})),T();return;case"Escape":if(t.state.comboboxState!==0)return;O.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&O.stopPropagation(),(0,Tn.flushSync)(()=>t.actions.closeCombobox()),T();return;default:return}}),h=E(O=>{O.preventDefault(),!_e(O.currentTarget)&&(O.button===0&&(t.state.comboboxState===0?t.actions.closeCombobox():t.actions.openCombobox()),T())}),y=Fe([a]),{isFocusVisible:v,focusProps:x}=ce({autoFocus:u}),{isHovered:R,hoverProps:$}=fe({isDisabled:c}),{pressed:A,pressProps:B}=Ce({disabled:c}),S=(0,Z.useMemo)(()=>({open:d===0,active:A||d===0,disabled:c,invalid:o.invalid,value:o.value,hover:R,focus:v}),[o,R,v,A,c,d]),C=ae({ref:s,id:a,type:Ke(e,r),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":m==null?void 0:m.id,"aria-expanded":d===0,"aria-labelledby":y,disabled:c||void 0,autoFocus:u,onPointerDown:h,onKeyDown:g},x,$,B);return N()({ourProps:C,theirProps:p,slot:S,defaultTag:id,name:"Combobox.Button"})}var ld="div",ad=3;function ud(e,n){var M,D,X;let t=(0,J.useId)(),{id:o=`headlessui-combobox-options-${t}`,hold:r=!1,anchor:i,portal:s=!1,modal:l=!0,transition:a=!1,...c}=e,u=Vn("Combobox.Options"),p=jn("Combobox.Options"),d=qt(i);d&&(s=!0);let[f,m]=Zt(d),[T,b]=(0,Z.useState)(null),g=Qt(),h=z(n,d?f:null,u.actions.setOptionsElement,b),[y,v,x,R,$]=ee(u,re=>[re.comboboxState,re.inputElement,re.buttonElement,re.optionsElement,re.activationTrigger]),A=Re(v||x),B=Re(R),S=He(),[C,L]=Ze(a,T,S!==null?(S&1)===1:y===0);vt(C,v,u.actions.closeCombobox);let O=p.__demoMode?!1:l&&y===0;xt(O,B);let P=p.__demoMode?!1:l&&y===0;Wt(P,{allowed:(0,Z.useCallback)(()=>[v,x,R],[v,x,R])}),j(()=>{var re;p.optionsPropsRef.current.static=(re=e.static)!=null?re:!1},[p.optionsPropsRef,e.static]),j(()=>{p.optionsPropsRef.current.hold=r},[p.optionsPropsRef,r]),ur(y===0,{container:R,accept(re){return re.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:re.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(re){re.setAttribute("role","none")}});let V=Fe([x==null?void 0:x.id]),k=(0,Z.useMemo)(()=>({open:y===0,option:void 0}),[y]),G=E(()=>{u.actions.setActivationTrigger(0)}),ne=E(re=>{re.preventDefault(),u.actions.setActivationTrigger(0)}),F=ae(d?g():{},{"aria-labelledby":V,role:"listbox","aria-multiselectable":p.mode===1?!0:void 0,id:o,ref:h,style:{...c.style,...m,"--input-width":It(v,!0).width,"--button-width":It(x,!0).width},onWheel:$===0?void 0:G,onMouseDown:ne,...Qe(L)}),I=C&&y===1,U=Hn(I,(M=p.virtual)==null?void 0:M.options),W=Hn(I,p.value),Y=E(re=>p.compare(W,re)),K=(0,Z.useMemo)(()=>{if(!p.virtual)return p;if(U===void 0)throw new Error("Missing `options` in virtual mode");return U!==p.virtual.options?{...p,virtual:{...p.virtual,options:U}}:p},[p,U,(D=p.virtual)==null?void 0:D.options]);p.virtual&&Object.assign(c,{children:Z.default.createElement(Lo.Provider,{value:K},Z.default.createElement(ed,{slot:k},c.children))});let w=N(),H=(0,Z.useMemo)(()=>p.mode===1?p:{...p,isSelected:Y},[p,Y]);return Z.default.createElement(lt,{enabled:s?e.static||C:!1,ownerDocument:A},Z.default.createElement(Lo.Provider,{value:H},w({ourProps:F,theirProps:{...c,children:Z.default.createElement(ya,{freeze:I},typeof c.children=="function"?(X=c.children)==null?void 0:X.call(c,k):c.children)},slot:k,defaultTag:ld,features:ad,visible:C,name:"Combobox.Options"})))}var cd="div";function fd(e,n){var O,P,V;let t=jn("Combobox.Option"),o=Vn("Combobox.Option"),r=(0,J.useId)(),{id:i=`headlessui-combobox-option-${r}`,value:s,disabled:l=(V=(P=(O=t.virtual)==null?void 0:O.disabled)==null?void 0:P.call(O,s))!=null?V:!1,order:a=null,...c}=e,[u]=ee(o,k=>[k.inputElement]),p=gi(u),d=ee(o,(0,Z.useCallback)(k=>o.selectors.isActive(k,s,i),[s,i])),f=t.isSelected(s),m=(0,Z.useRef)(null),T=me({disabled:l,value:s,domRef:m,order:a}),b=(0,Z.useContext)(Aa),g=z(n,m,b?b.measureElement:null),h=E(()=>{o.actions.setIsTyping(!1),o.actions.onChange(s)});j(()=>o.actions.registerOption(i,T),[T,i]);let y=ee(o,(0,Z.useCallback)(k=>o.selectors.shouldScrollIntoView(k,s,i),[s,i]));j(()=>{if(y)return he().requestAnimationFrame(()=>{var k,G;(G=(k=m.current)==null?void 0:k.scrollIntoView)==null||G.call(k,{block:"nearest"})})},[y,m]);let v=E(k=>{k.preventDefault(),k.button===0&&(l||(h(),go()||requestAnimationFrame(()=>p()),t.mode===0&&o.actions.closeCombobox()))}),x=E(()=>{if(l)return o.actions.goToOption({focus:5});let k=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:k})}),R=Ln(),$=E(k=>R.update(k)),A=E(k=>{if(!R.wasMoved(k)||l||d&&o.state.activationTrigger===0)return;let G=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:G},0)}),B=E(k=>{R.wasMoved(k)&&(l||d&&(t.optionsPropsRef.current.hold||o.state.activationTrigger===0&&o.actions.goToOption({focus:5})))}),S=(0,Z.useMemo)(()=>({active:d,focus:d,selected:f,disabled:l}),[d,f,l]),C={id:i,ref:g,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":f,disabled:void 0,onMouseDown:v,onFocus:x,onPointerEnter:$,onMouseEnter:$,onPointerMove:A,onMouseMove:A,onPointerLeave:B,onMouseLeave:B};return N()({ourProps:C,theirProps:c,slot:S,defaultTag:cd,name:"Combobox.Option"})}var dd=_(nd),Oa=_(sd),Da=_(rd),La=nt,Ia=_(ud),Ma=_(fd),pd=Object.assign(dd,{Input:Da,Button:Oa,Label:La,Options:Ia,Option:Ma});var Rr=require("react");var md=Rr.Fragment;function Td(e,n){let{...t}=e,o=!1,{isFocusVisible:r,focusProps:i}=ce(),{isHovered:s,hoverProps:l}=fe({isDisabled:o}),{pressed:a,pressProps:c}=Ce({disabled:o}),u=ae({ref:n},i,l,c),p=(0,Rr.useMemo)(()=>({hover:s,focus:r,active:a}),[s,r,a]);return N()({ourProps:u,theirProps:t,slot:p,defaultTag:md,name:"DataInteractive"})}var bd=_(Td);var se=le(require("react"),1);function Fa(e,n=typeof document!="undefined"?document.defaultView:null,t){let o=Mt(e,"escape");Kt(n,"keydown",r=>{o&&(r.defaultPrevented||r.key==="Escape"&&t(r))})}var Gi=require("react");function wa(){var o;let[e]=(0,Gi.useState)(()=>typeof window!="undefined"&&typeof window.matchMedia=="function"?window.matchMedia("(pointer: coarse)"):null),[n,t]=(0,Gi.useState)((o=e==null?void 0:e.matches)!=null?o:!1);return j(()=>{if(!e)return;function r(i){t(i.matches)}return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[e]),n}var Nt=le(require("react"),1);function Sr({defaultContainers:e=[],portals:n,mainTreeNode:t}={}){let o=Re(t),r=E(()=>{var s,l;let i=[];for(let a of e)a!==null&&(ut(a)?i.push(a):"current"in a&&ut(a.current)&&i.push(a.current));if(n!=null&&n.current)for(let a of n.current)i.push(a);for(let a of(s=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?s:[])a!==document.body&&a!==document.head&&ut(a)&&a.id!=="headlessui-portal-root"&&(t&&(a.contains(t)||a.contains((l=t==null?void 0:t.getRootNode())==null?void 0:l.host))||i.some(c=>a.contains(c))||i.push(a));return i});return{resolveContainers:r,contains:E(i=>r().some(s=>s.contains(i)))}}var _a=(0,Nt.createContext)(null);function Kn({children:e,node:n}){let[t,o]=(0,Nt.useState)(null),r=Io(n!=null?n:t);return Nt.default.createElement(_a.Provider,{value:r},e,r===null&&Nt.default.createElement(ke,{features:4,ref:i=>{var s,l;if(i){for(let a of(l=(s=Pe(i))==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?l:[])if(a!==document.body&&a!==document.head&&ut(a)&&a!=null&&a.contains(i)){o(a);break}}}}))}function Io(e=null){var n;return(n=(0,Nt.useContext)(_a))!=null?n:e}var Ct=le(require("react"),1);var $a=require("react");function gn(){let e=(0,$a.useRef)(!1);return j(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var ka=require("react");function Mo(){let e=(0,ka.useRef)(0);return lr(!0,"keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function Ha(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)ut(t.current)&&n.add(t.current);return n}var yd="div",Cr=(s=>(s[s.None=0]="None",s[s.InitialFocus=1]="InitialFocus",s[s.TabLock=2]="TabLock",s[s.FocusLock=4]="FocusLock",s[s.RestoreFocus=8]="RestoreFocus",s[s.AutoFocus=16]="AutoFocus",s))(Cr||{});function hd(e,n){let t=(0,Ct.useRef)(null),o=z(t,n),{initialFocus:r,initialFocusFallback:i,containers:s,features:l=15,...a}=e;Ht()||(l=0);let c=Re(t);xd(l,{ownerDocument:c});let u=Pd(l,{ownerDocument:c,container:t,initialFocus:r,initialFocusFallback:i});Rd(l,{ownerDocument:c,container:t,containers:s,previousActiveElement:u});let p=Mo(),d=E(h=>{if(!Te(t.current))return;let y=t.current;(x=>x())(()=>{q(p.current,{[0]:()=>{ve(y,1,{skipElements:[h.relatedTarget,i]})},[1]:()=>{ve(y,8,{skipElements:[h.relatedTarget,i]})}})})}),f=Mt(!!(l&2),"focus-trap#tab-lock"),m=Se(),T=(0,Ct.useRef)(!1),b={ref:o,onKeyDown(h){h.key=="Tab"&&(T.current=!0,m.requestAnimationFrame(()=>{T.current=!1}))},onBlur(h){if(!(l&4))return;let y=Ha(s);Te(t.current)&&y.add(t.current);let v=h.relatedTarget;Le(v)&&v.dataset.headlessuiFocusGuard!=="true"&&(Na(y,v)||(T.current?ve(t.current,q(p.current,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:h.target}):Le(h.target)&&dt(h.target)))}},g=N();return Ct.default.createElement(Ct.default.Fragment,null,f&&Ct.default.createElement(ke,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}),g({ourProps:b,theirProps:a,defaultTag:yd,name:"FocusTrap"}),f&&Ct.default.createElement(ke,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}))}var vd=_(hd),Vi=Object.assign(vd,{features:Cr});function Ed(e=!0){let n=(0,Ct.useRef)(it.slice());return un(([t],[o])=>{o===!0&&t===!1&&Dt(()=>{n.current.splice(0)}),o===!1&&t===!0&&(n.current=it.slice())},[e,it,n]),E(()=>{var t;return(t=n.current.find(o=>o!=null&&o.isConnected))!=null?t:null})}function xd(e,{ownerDocument:n}){let t=!!(e&8),o=Ed(t);un(()=>{t||(n==null?void 0:n.activeElement)===(n==null?void 0:n.body)&&dt(o())},[t]),st(()=>{t&&dt(o())})}function Pd(e,{ownerDocument:n,container:t,initialFocus:o,initialFocusFallback:r}){let i=(0,Ct.useRef)(null),s=Mt(!!(e&1),"focus-trap#initial-focus"),l=gn();return un(()=>{if(e===0)return;if(!s){r!=null&&r.current&&dt(r.current);return}let a=t.current;a&&Dt(()=>{if(!l.current)return;let c=n==null?void 0:n.activeElement;if(o!=null&&o.current){if((o==null?void 0:o.current)===c){i.current=c;return}}else if(a.contains(c)){i.current=c;return}if(o!=null&&o.current)dt(o.current);else{if(e&16){if(ve(a,65)!==0)return}else if(ve(a,1)!==0)return;if(r!=null&&r.current&&(dt(r.current),(n==null?void 0:n.activeElement)===r.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=n==null?void 0:n.activeElement})},[r,s,e]),i}function Rd(e,{ownerDocument:n,container:t,containers:o,previousActiveElement:r}){let i=gn(),s=!!(e&4);Kt(n==null?void 0:n.defaultView,"focus",l=>{if(!s||!i.current)return;let a=Ha(o);Te(t.current)&&a.add(t.current);let c=r.current;if(!c)return;let u=l.target;Te(u)?Na(a,u)?(r.current=u,dt(u)):(l.preventDefault(),l.stopPropagation(),dt(c)):dt(r.current)},!0)}function Na(e,n){for(let t of e)if(t.contains(n))return!0;return!1}var ie=le(require("react"),1);function Ba(e){var n;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((n=e.as)!=null?n:Ua)!==ie.Fragment||ie.default.Children.count(e.children)===1}var Ar=(0,ie.createContext)(null);Ar.displayName="TransitionContext";function Sd(){let e=(0,ie.useContext)(Ar);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function Cd(){let e=(0,ie.useContext)(Or);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}var Or=(0,ie.createContext)(null);Or.displayName="NestingContext";function Dr(e){return"children"in e?Dr(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function Ga(e,n){let t=me(e),o=(0,ie.useRef)([]),r=gn(),i=Se(),s=E((f,m=1)=>{let T=o.current.findIndex(({el:b})=>b===f);T!==-1&&(q(m,{[0](){o.current.splice(T,1)},[1](){o.current[T].state="hidden"}}),i.microTask(()=>{var b;!Dr(o)&&r.current&&((b=t.current)==null||b.call(t))}))}),l=E(f=>{let m=o.current.find(({el:T})=>T===f);return m?m.state!=="visible"&&(m.state="visible"):o.current.push({el:f,state:"visible"}),()=>s(f,0)}),a=(0,ie.useRef)([]),c=(0,ie.useRef)(Promise.resolve()),u=(0,ie.useRef)({enter:[],leave:[]}),p=E((f,m,T)=>{a.current.splice(0),n&&(n.chains.current[m]=n.chains.current[m].filter(([b])=>b!==f)),n==null||n.chains.current[m].push([f,new Promise(b=>{a.current.push(b)})]),n==null||n.chains.current[m].push([f,new Promise(b=>{Promise.all(u.current[m].map(([g,h])=>h)).then(()=>b())})]),m==="enter"?c.current=c.current.then(()=>n==null?void 0:n.wait.current).then(()=>T(m)):T(m)}),d=E((f,m,T)=>{Promise.all(u.current[m].splice(0).map(([b,g])=>g)).then(()=>{var b;(b=a.current.shift())==null||b()}).then(()=>T(m))});return(0,ie.useMemo)(()=>({children:o,register:l,unregister:s,onStart:p,onStop:d,wait:c,chains:u}),[l,s,o,p,d,u,c])}var Ua=ie.Fragment,Va=1;function Ad(e,n){var w,H;let{transition:t=!0,beforeEnter:o,afterEnter:r,beforeLeave:i,afterLeave:s,enter:l,enterFrom:a,enterTo:c,entered:u,leave:p,leaveFrom:d,leaveTo:f,...m}=e,[T,b]=(0,ie.useState)(null),g=(0,ie.useRef)(null),h=Ba(e),y=z(...h?[g,n,b]:n===null?[]:[n]),v=(w=m.unmount)==null||w?0:1,{show:x,appear:R,initial:$}=Sd(),[A,B]=(0,ie.useState)(x?"visible":"hidden"),S=Cd(),{register:C,unregister:L}=S;j(()=>C(g),[C,g]),j(()=>{if(v===1&&g.current){if(x&&A!=="visible"){B("visible");return}return q(A,{["hidden"]:()=>L(g),["visible"]:()=>C(g)})}},[A,g,C,L,x,v]);let O=Ht();j(()=>{if(h&&O&&A==="visible"&&g.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[g,A,O,h]);let P=$&&!R,V=R&&x&&$,k=(0,ie.useRef)(!1),G=Ga(()=>{k.current||(B("hidden"),L(g))},S),ne=E(M=>{k.current=!0;let D=M?"enter":"leave";G.onStart(g,D,X=>{X==="enter"?o==null||o():X==="leave"&&(i==null||i())})}),F=E(M=>{let D=M?"enter":"leave";k.current=!1,G.onStop(g,D,X=>{X==="enter"?r==null||r():X==="leave"&&(s==null||s())}),D==="leave"&&!Dr(G)&&(B("hidden"),L(g))});(0,ie.useEffect)(()=>{h&&t||(ne(x),F(x))},[x,h,t]);let I=(()=>!(!t||!h||!O||P))(),[,U]=Ze(I,T,x,{start:ne,end:F}),W=Tt({ref:y,className:((H=co(m.className,V&&l,V&&a,U.enter&&l,U.enter&&U.closed&&a,U.enter&&!U.closed&&c,U.leave&&p,U.leave&&!U.closed&&d,U.leave&&U.closed&&f,!U.transition&&x&&u))==null?void 0:H.trim())||void 0,...Qe(U)}),Y=0;A==="visible"&&(Y|=1),A==="hidden"&&(Y|=2),x&&A==="hidden"&&(Y|=8),!x&&A==="visible"&&(Y|=4);let K=N();return ie.default.createElement(Or.Provider,{value:G},ie.default.createElement(rt,{value:Y},K({ourProps:W,theirProps:m,defaultTag:Ua,features:Va,visible:A==="visible",name:"Transition.Child"})))}function Od(e,n){let{show:t,appear:o=!1,unmount:r=!0,...i}=e,s=(0,ie.useRef)(null),l=Ba(e),a=z(...l?[s,n]:n===null?[]:[n]);Ht();let c=He();if(t===void 0&&c!==null&&(t=(c&1)===1),t===void 0)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,p]=(0,ie.useState)(t?"visible":"hidden"),d=Ga(()=>{t||p("hidden")}),[f,m]=(0,ie.useState)(!0),T=(0,ie.useRef)([t]);j(()=>{f!==!1&&T.current[T.current.length-1]!==t&&(T.current.push(t),m(!1))},[T,t]);let b=(0,ie.useMemo)(()=>({show:t,appear:o,initial:f}),[t,o,f]);j(()=>{t?p("visible"):!Dr(d)&&s.current!==null&&p("hidden")},[t,d]);let g={unmount:r},h=E(()=>{var x;f&&m(!1),(x=e.beforeEnter)==null||x.call(e)}),y=E(()=>{var x;f&&m(!1),(x=e.beforeLeave)==null||x.call(e)}),v=N();return ie.default.createElement(Or.Provider,{value:d},ie.default.createElement(Ar.Provider,{value:b},v({ourProps:{...g,as:ie.Fragment,children:ie.default.createElement(Wa,{ref:a,...g,...i,beforeEnter:h,beforeLeave:y})},theirProps:{},defaultTag:ie.Fragment,features:Va,visible:u==="visible",name:"Transition"})))}function Dd(e,n){let t=(0,ie.useContext)(Ar)!==null,o=He()!==null;return ie.default.createElement(ie.default.Fragment,null,!t&&o?ie.default.createElement(Wi,{ref:n,...e}):ie.default.createElement(Wa,{ref:n,...e}))}var Wi=_(Od),Wa=_(Ad),wo=_(Dd),ji=Object.assign(Wi,{Child:wo,Root:Wi});var Ld={[0](e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},Ki=(0,se.createContext)(null);Ki.displayName="DialogContext";function Lr(e){let n=(0,se.useContext)(Ki);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Lr),t}return n}function Id(e,n){return q(n.type,Ld,e,n)}var ja=_(function(n,t){let o=(0,J.useId)(),{id:r=`headlessui-dialog-${o}`,open:i,onClose:s,initialFocus:l,role:a="dialog",autoFocus:c=!0,__demoMode:u=!1,unmount:p=!1,...d}=n,f=(0,se.useRef)(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(f.current||(f.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=He();i===void 0&&m!==null&&(i=(m&1)===1);let T=(0,se.useRef)(null),b=z(T,t),g=Re(T),h=i?0:1,[y,v]=(0,se.useReducer)(Id,{titleId:null,descriptionId:null,panelRef:(0,se.createRef)()}),x=E(()=>s(!1)),R=E(M=>v({type:0,id:M})),A=Ht()?h===0:!1,[B,S]=Pr(),C={get current(){var M;return(M=y.panelRef.current)!=null?M:T.current}},L=Io(),{resolveContainers:O}=Sr({mainTreeNode:L,portals:B,defaultContainers:[C]}),P=m!==null?(m&4)===4:!1;Wt(u||P?!1:A,{allowed:E(()=>{var M,D;return[(D=(M=T.current)==null?void 0:M.closest("[data-headlessui-portal]"))!=null?D:null]}),disallowed:E(()=>{var M;return[(M=L==null?void 0:L.closest("body > *:not(#headlessui-portal-root)"))!=null?M:null]})});let k=$e.get(null);j(()=>{if(A)return k.actions.push(r),()=>k.actions.pop(r)},[k,r,A]);let G=ee(k,(0,se.useCallback)(M=>k.selectors.isTop(M,r),[k,r]));Et(G,O,M=>{M.preventDefault(),x()}),Fa(G,g==null?void 0:g.defaultView,M=>{M.preventDefault(),M.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),x()}),xt(u||P?!1:A,g,O),vt(A,T,x);let[F,I]=ct(),U=(0,se.useMemo)(()=>[{dialogState:h,close:x,setTitleId:R,unmount:p},y],[h,y,x,R,p]),W=(0,se.useMemo)(()=>({open:h===0}),[h]),Y={ref:b,id:r,role:a,tabIndex:-1,"aria-modal":u?void 0:h===0?!0:void 0,"aria-labelledby":y.titleId,"aria-describedby":F,unmount:p},K=!wa(),w=0;A&&!u&&(w|=8,w|=2,c&&(w|=16),K&&(w|=1));let H=N();return se.default.createElement(Nn,null,se.default.createElement(_i,{force:!0},se.default.createElement(lt,null,se.default.createElement(Ki.Provider,{value:U},se.default.createElement(ki,{target:T},se.default.createElement(_i,{force:!1},se.default.createElement(I,{slot:W},se.default.createElement(S,null,se.default.createElement(Vi,{initialFocus:l,initialFocusFallback:T,containers:O,features:w},se.default.createElement(rn,{value:x},H({ourProps:Y,theirProps:d,slot:W,defaultTag:Md,features:Fd,visible:h===0,name:"Dialog"})))))))))))}),Md="div",Fd=3;function wd(e,n){let{transition:t=!1,open:o,...r}=e,i=He(),s=e.hasOwnProperty("open")||i!==null,l=e.hasOwnProperty("onClose");if(!s&&!l)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!l)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(o!==void 0||t)&&!r.static?se.default.createElement(Kn,null,se.default.createElement(ji,{show:o,transition:t,unmount:r.unmount},se.default.createElement(ja,{ref:n,...r}))):se.default.createElement(Kn,null,se.default.createElement(ja,{ref:n,open:o,...r}))}var _d="div";function $d(e,n){let t=(0,J.useId)(),{id:o=`headlessui-dialog-panel-${t}`,transition:r=!1,...i}=e,[{dialogState:s,unmount:l},a]=Lr("Dialog.Panel"),c=z(n,a.panelRef),u=(0,se.useMemo)(()=>({open:s===0}),[s]),p=E(b=>{b.stopPropagation()}),d={ref:c,id:o,onClick:p},f=r?wo:se.Fragment,m=r?{unmount:l}:{},T=N();return se.default.createElement(f,{...m},T({ourProps:d,theirProps:i,slot:u,defaultTag:_d,name:"Dialog.Panel"}))}var kd="div";function Hd(e,n){let{transition:t=!1,...o}=e,[{dialogState:r,unmount:i}]=Lr("Dialog.Backdrop"),s=(0,se.useMemo)(()=>({open:r===0}),[r]),l={ref:n,"aria-hidden":!0},a=t?wo:se.Fragment,c=t?{unmount:i}:{},u=N();return se.default.createElement(a,{...c},u({ourProps:l,theirProps:o,slot:s,defaultTag:kd,name:"Dialog.Backdrop"}))}var Nd="h2";function Bd(e,n){let t=(0,J.useId)(),{id:o=`headlessui-dialog-title-${t}`,...r}=e,[{dialogState:i,setTitleId:s}]=Lr("Dialog.Title"),l=z(n);(0,se.useEffect)(()=>(s(o),()=>s(null)),[o,s]);let a=(0,se.useMemo)(()=>({open:i===0}),[i]),c={ref:l,id:o};return N()({ourProps:c,theirProps:r,slot:a,defaultTag:Nd,name:"Dialog.Title"})}var Gd=_(wd),Ka=_($d),Ud=_(Hd),za=_(Bd),Vd=Lt,Wd=Object.assign(Gd,{Panel:Ka,Title:za,Description:Lt});var ue=le(require("react"),1);var Ya=le(require("react"),1),Xa,qa=(Xa=Ya.default.startTransition)!=null?Xa:function(n){n()};var jd={[0]:e=>({...e,disclosureState:q(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}},[4](e,n){return e.buttonElement===n.element?e:{...e,buttonElement:n.element}},[5](e,n){return e.panelElement===n.element?e:{...e,panelElement:n.element}}},zi=(0,ue.createContext)(null);zi.displayName="DisclosureContext";function Xi(e){let n=(0,ue.useContext)(zi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Xi),t}return n}var Yi=(0,ue.createContext)(null);Yi.displayName="DisclosureAPIContext";function Ja(e){let n=(0,ue.useContext)(Yi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ja),t}return n}var qi=(0,ue.createContext)(null);qi.displayName="DisclosurePanelContext";function Kd(){return(0,ue.useContext)(qi)}function zd(e,n){return q(n.type,jd,e,n)}var Xd=ue.Fragment;function Yd(e,n){let{defaultOpen:t=!1,...o}=e,r=(0,ue.useRef)(null),i=z(n,Pn(T=>{r.current=T},e.as===void 0||e.as===ue.Fragment)),s=(0,ue.useReducer)(zd,{disclosureState:t?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:l,buttonId:a},c]=s,u=E(T=>{c({type:1});let b=Pe(r);if(!b||!a)return;let g=(()=>T?Le(T)?T:"current"in T&&Le(T.current)?T.current:b.getElementById(a):b.getElementById(a))();g==null||g.focus()}),p=(0,ue.useMemo)(()=>({close:u}),[u]),d=(0,ue.useMemo)(()=>({open:l===0,close:u}),[l,u]),f={ref:i},m=N();return ue.default.createElement(zi.Provider,{value:s},ue.default.createElement(Yi.Provider,{value:p},ue.default.createElement(rn,{value:u},ue.default.createElement(rt,{value:q(l,{[0]:1,[1]:2})},m({ourProps:f,theirProps:o,slot:d,defaultTag:Xd,name:"Disclosure"})))))}var qd="button";function Jd(e,n){let t=(0,J.useId)(),{id:o=`headlessui-disclosure-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[l,a]=Xi("Disclosure.Button"),c=Kd(),u=c===null?!1:c===l.panelId,p=(0,ue.useRef)(null),d=z(p,n,E(S=>{if(!u)return a({type:4,element:S})}));(0,ue.useEffect)(()=>{if(!u)return a({type:2,buttonId:o}),()=>{a({type:2,buttonId:null})}},[o,a,u]);let f=E(S=>{var C;if(u){if(l.disclosureState===1)return;switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0}),(C=l.buttonElement)==null||C.focus();break}}else switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0});break}}),m=E(S=>{switch(S.key){case" ":S.preventDefault();break}}),T=E(S=>{var C;_e(S.currentTarget)||r||(u?(a({type:0}),(C=l.buttonElement)==null||C.focus()):a({type:0}))}),{isFocusVisible:b,focusProps:g}=ce({autoFocus:i}),{isHovered:h,hoverProps:y}=fe({isDisabled:r}),{pressed:v,pressProps:x}=Ce({disabled:r}),R=(0,ue.useMemo)(()=>({open:l.disclosureState===0,hover:h,active:v,disabled:r,focus:b,autofocus:i}),[l,h,v,b,r,i]),$=Ke(e,l.buttonElement),A=u?ae({ref:d,type:$,disabled:r||void 0,autoFocus:i,onKeyDown:f,onClick:T},g,y,x):ae({ref:d,id:o,type:$,"aria-expanded":l.disclosureState===0,"aria-controls":l.panelElement?l.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:f,onKeyUp:m,onClick:T},g,y,x);return N()({ourProps:A,theirProps:s,slot:R,defaultTag:qd,name:"Disclosure.Button"})}var Qd="div",Zd=3;function ep(e,n){let t=(0,J.useId)(),{id:o=`headlessui-disclosure-panel-${t}`,transition:r=!1,...i}=e,[s,l]=Xi("Disclosure.Panel"),{close:a}=Ja("Disclosure.Panel"),[c,u]=(0,ue.useState)(null),p=z(n,E(h=>{qa(()=>l({type:5,element:h}))}),u);(0,ue.useEffect)(()=>(l({type:3,panelId:o}),()=>{l({type:3,panelId:null})}),[o,l]);let d=He(),[f,m]=Ze(r,c,d!==null?(d&1)===1:s.disclosureState===0),T=(0,ue.useMemo)(()=>({open:s.disclosureState===0,close:a}),[s.disclosureState,a]),b={ref:p,id:o,...Qe(m)},g=N();return ue.default.createElement(Nn,null,ue.default.createElement(qi.Provider,{value:s.panelId},g({ourProps:b,theirProps:i,slot:T,defaultTag:Qd,features:Zd,visible:f,name:"Disclosure.Panel"})))}var tp=_(Yd),Qa=_(Jd),Za=_(ep),np=Object.assign(tp,{Button:Qa,Panel:Za});var tn=le(require("react"),1);var op="div";function rp(e,n){let t=`headlessui-control-${(0,J.useId)()}`,[o,r]=Be(),[i,s]=ct(),l=ge(),{disabled:a=l||!1,...c}=e,u=(0,tn.useMemo)(()=>({disabled:a}),[a]),p={ref:n,disabled:a||void 0,"aria-disabled":a||void 0},d=N();return tn.default.createElement(Yo,{value:a},tn.default.createElement(r,{value:o},tn.default.createElement(s,{value:i},tn.default.createElement(Bs,{id:t},d({ourProps:p,theirProps:{...c,children:tn.default.createElement(Hs,null,typeof c.children=="function"?c.children(u):c.children)},slot:u,defaultTag:op,name:"Field"})))))}var ip=_(rp);var _o=le(require("react"),1);var Ir=require("react");function eu(e){let n=typeof e=="string"?e:void 0,[t,o]=(0,Ir.useState)(n);return[n!=null?n:t,(0,Ir.useCallback)(r=>{n||Te(r)&&o(r.tagName.toLowerCase())},[n])]}var tu="fieldset";function sp(e,n){var f;let t=ge(),{disabled:o=t||!1,...r}=e,[i,s]=eu((f=e.as)!=null?f:tu),l=z(n,s),[a,c]=Be(),u=(0,_o.useMemo)(()=>({disabled:o}),[o]),p=i==="fieldset"?{ref:l,"aria-labelledby":a,disabled:o||void 0}:{ref:l,role:"group","aria-labelledby":a,"aria-disabled":o||void 0},d=N();return _o.default.createElement(Yo,{value:o},_o.default.createElement(c,null,d({ourProps:p,theirProps:r,slot:u,defaultTag:tu,name:"Fieldset"})))}var lp=_(sp);var nu=require("react");var ap="input";function up(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-input-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocused:d,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,nu.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:d,autofocus:l}),[s,a,m,d,l]);return N()({ourProps:b,theirProps:c,slot:g,defaultTag:ap,name:"Input"})}var cp=_(up);var ou=le(require("react"),1);function fp(e,n){return ou.default.createElement(nt,{as:"div",ref:n,...e})}var dp=_(fp);var oe=le(require("react"),1),Yn=require("react-dom");var ru=require("react");function Mr(e,n){let t=(0,ru.useRef)({left:0,top:0});if(j(()=>{if(!n)return;let i=n.getBoundingClientRect();i&&(t.current=i)},[e,n]),n==null||!e||n===document.activeElement)return!1;let o=n.getBoundingClientRect();return o.top!==t.current.top||o.left!==t.current.left}var Ji=require("react");var iu=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function su(e){var i,s;let n=(i=e.innerText)!=null?i:"",t=e.cloneNode(!0);if(!Te(t))return n;let o=!1;for(let l of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))l.remove(),o=!0;let r=o?(s=t.innerText)!=null?s:"":n;return iu.test(r)&&(r=r.replace(iu,"")),r}function lu(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let o=t.split(" ").map(r=>{let i=document.getElementById(r);if(i){let s=i.getAttribute("aria-label");return typeof s=="string"?s.trim():su(i).trim()}return null}).filter(Boolean);if(o.length>0)return o.join(", ")}return su(e).trim()}function Fr(e){let n=(0,Ji.useRef)(""),t=(0,Ji.useRef)("");return E(()=>{let o=e.current;if(!o)return"";let r=o.innerText;if(n.current===r)return t.current;let i=lu(o).trim().toLowerCase();return n.current=r,t.current=i,i})}function au(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=Ve(n(e.options.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}var pp={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,pendingFocus:{focus:5},listboxState:1,__demoMode:!1}},[0](e,n){if(e.dataRef.current.disabled||e.listboxState===0)return e;let t=e.activeOptionIndex,{isSelected:o}=e.dataRef.current,r=e.options.findIndex(i=>o(i.dataRef.current.value));return r!==-1&&(t=r),{...e,pendingFocus:n.focus,listboxState:0,activeOptionIndex:t,__demoMode:!1}},[2](e,n){var i,s,l,a,c;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeOptionIndex:null};if(n.focus===4)return{...t,activeOptionIndex:e.options.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=tt(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeOptionIndex:d}}}}else if(n.focus===2){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=tt(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((a=p.current)==null?void 0:a.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeOptionIndex:d}}}}let o=au(e),r=tt(n,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeOptionIndex:r}},[3]:(e,n)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+o).concat(e.options.slice(0,e.activeOptionIndex+o)):e.options).find(a=>{var c;return!a.dataRef.current.disabled&&((c=a.dataRef.current.textValue)==null?void 0:c.startsWith(r))}),l=s?e.options.indexOf(s):-1;return l===-1||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,n)=>{let t=e.options.concat(n.options),o=e.activeOptionIndex;if(e.pendingFocus.focus!==5&&(o=tt(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled})),e.activeOptionIndex===null){let{isSelected:r}=e.dataRef.current;if(r){let i=t.findIndex(s=>r==null?void 0:r(s.dataRef.current.value));i!==-1&&(o=i)}}return{...e,options:t,activeOptionIndex:o,pendingFocus:{focus:5},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.options,o=[],r=new Set(n.options);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...au(e),pendingShouldSort:!1}:e},zn=class extends ot{constructor(t){super(t);Oe(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return q(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(c=>r(c,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:ln(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:ln(()=>{let t=[];return[o=>t.push(o),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:ln(()=>{let t=null;return[(o,r)=>{t={type:2,...o,trigger:r}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:t=>{this.send({type:0,focus:t})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:o}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:4,id:o})}},selectOption:t=>{let o=this.state.options.find(r=>r.id===t);o&&this.actions.onChange(o.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});Oe(this,"selectors",{activeDescendantId(t){var i;let o=t.activeOptionIndex,r=t.options;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeOptionIndex,i=t.options;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.listboxState===0&&this.actions.closeListbox()})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new zn({id:t,dataRef:{current:{}},listboxState:o?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:5},__demoMode:o})}reduce(t,o){return q(o.type,pp,t,o)}};var Xn=require("react");var Qi=(0,Xn.createContext)(null);function wr(e){let n=(0,Xn.useContext)(Qi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Zi),t}return n}function Zi({id:e,__demoMode:n=!1}){let t=(0,Xn.useMemo)(()=>zn.new({id:e,__demoMode:n}),[]);return st(()=>t.dispose()),t}var _r=(0,oe.createContext)(null);_r.displayName="ListboxDataContext";function $o(e){let n=(0,oe.useContext)(_r);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,$o),t}return n}var mp=oe.Fragment;function Tp(e,n){let t=(0,J.useId)(),o=ge(),{value:r,defaultValue:i,form:s,name:l,onChange:a,by:c,invalid:u=!1,disabled:p=o||!1,horizontal:d=!1,multiple:f=!1,__demoMode:m=!1,...T}=e,b=d?"horizontal":"vertical",g=z(n),h=gt(i),[y=f?[]:void 0,v]=bt(r,a,h),x=Zi({id:t,__demoMode:m}),R=(0,oe.useRef)({static:!1,hold:!1}),$=(0,oe.useRef)(new Map),A=Sn(c),B=(0,oe.useCallback)(W=>q(S.mode,{[1]:()=>y.some(Y=>A(Y,W)),[0]:()=>A(y,W)}),[y]),S=(0,oe.useMemo)(()=>({value:y,disabled:p,invalid:u,mode:f?1:0,orientation:b,onChange:v,compare:A,isSelected:B,optionsPropsRef:R,listRef:$}),[y,p,u,f,b,v,A,B,R,$]);j(()=>{x.state.dataRef.current=S},[S]);let C=ee(x,W=>W.listboxState),L=$e.get(null),O=ee(L,(0,oe.useCallback)(W=>L.selectors.isTop(W,t),[L,t])),[P,V]=ee(x,W=>[W.buttonElement,W.optionsElement]);Et(O,[P,V],(W,Y)=>{x.send({type:1}),Ft(Y,1)||(W.preventDefault(),P==null||P.focus())});let k=(0,oe.useMemo)(()=>({open:C===0,disabled:p,invalid:u,value:y}),[C,p,u,y]),[G,ne]=Be({inherit:!0}),F={ref:g},I=(0,oe.useCallback)(()=>{if(h!==void 0)return v==null?void 0:v(h)},[v,h]),U=N();return oe.default.createElement(ne,{value:G,props:{htmlFor:P==null?void 0:P.id},slot:{open:C===0,disabled:p}},oe.default.createElement(en,null,oe.default.createElement(Qi.Provider,{value:x},oe.default.createElement(_r.Provider,{value:S},oe.default.createElement(rt,{value:q(C,{[0]:1,[1]:2})},l!=null&&y!=null&&oe.default.createElement(yt,{disabled:p,data:{[l]:y},form:s,onReset:I}),U({ourProps:F,theirProps:T,slot:k,defaultTag:mp,name:"Listbox"}))))))}var bp="button";function gp(e,n){let t=(0,J.useId)(),o=we(),r=$o("Listbox.Button"),i=wr("Listbox.Button"),{id:s=o||`headlessui-listbox-button-${t}`,disabled:l=r.disabled||!1,autoFocus:a=!1,...c}=e,u=z(n,Jt(),i.actions.setButtonElement),p=vr(),[d,f,m]=ee(i,F=>[F.listboxState,F.buttonElement,F.optionsElement]),T=d===0;Dn(T,{trigger:f,action:(0,oe.useCallback)(F=>{if(f!=null&&f.contains(F.target))return Ge.Ignore;let I=F.target.closest('[role="option"]:not([data-disabled])');return Te(I)?Ge.Select(I):m!=null&&m.contains(F.target)?Ge.Ignore:Ge.Close},[f,m]),close:i.actions.closeListbox,select:i.actions.selectActiveOption});let b=E(F=>{switch(F.key){case"Enter":Ut(F.currentTarget);break;case" ":case"ArrowDown":F.preventDefault(),i.actions.openListbox({focus:r.value?5:0});break;case"ArrowUp":F.preventDefault(),i.actions.openListbox({focus:r.value?5:3});break}}),g=E(F=>{switch(F.key){case" ":F.preventDefault();break}}),h=E(F=>{var I;if(F.button===0){if(_e(F.currentTarget))return F.preventDefault();i.state.listboxState===0?((0,Yn.flushSync)(()=>i.actions.closeListbox()),(I=i.state.buttonElement)==null||I.focus({preventScroll:!0})):(F.preventDefault(),i.actions.openListbox({focus:5}))}}),y=(0,oe.useRef)(null),v=E(F=>{y.current=F.pointerType,F.pointerType==="mouse"&&h(F)}),x=E(F=>{y.current!=="mouse"&&h(F)}),R=E(F=>F.preventDefault()),$=Fe([s]),A=Ne(),{isFocusVisible:B,focusProps:S}=ce({autoFocus:a}),{isHovered:C,hoverProps:L}=fe({isDisabled:l}),{pressed:O,pressProps:P}=Ce({disabled:l}),V=(0,oe.useMemo)(()=>({open:d===0,active:O||d===0,disabled:l,invalid:r.invalid,value:r.value,hover:C,focus:B,autofocus:a}),[d,r.value,l,C,B,O,r.invalid,a]),k=ee(i,F=>F.listboxState===0),G=ae(p(),{ref:u,id:s,type:Ke(e,f),"aria-haspopup":"listbox","aria-controls":m==null?void 0:m.id,"aria-expanded":k,"aria-labelledby":$,"aria-describedby":A,disabled:l||void 0,autoFocus:a,onKeyDown:b,onKeyUp:g,onKeyPress:R,onPointerDown:v,onClick:x},S,L,P);return N()({ourProps:G,theirProps:c,slot:V,defaultTag:bp,name:"Listbox.Button"})}var uu=(0,oe.createContext)(!1),yp="div",hp=3;function vp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-listbox-options-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,c=qt(r),[u,p]=(0,oe.useState)(null);c&&(i=!0);let d=$o("Listbox.Options"),f=wr("Listbox.Options"),[m,T,b,g]=ee(f,D=>[D.listboxState,D.buttonElement,D.optionsElement,D.__demoMode]),h=Re(T),y=Re(b),v=He(),[x,R]=Ze(l,u,v!==null?(v&1)===1:m===0);vt(x,T,f.actions.closeListbox);let $=g?!1:s&&m===0;xt($,y);let A=g?!1:s&&m===0;Wt(A,{allowed:(0,oe.useCallback)(()=>[T,b],[T,b])});let B=m!==0,C=Mr(B,T)?!1:x,L=x&&m===1,O=Hn(L,d.value),P=E(D=>d.compare(O,D)),V=ee(f,D=>{var re;if(c==null||!((re=c==null?void 0:c.to)!=null&&re.includes("selection")))return null;let X=D.options.findIndex(je=>P(je.dataRef.current.value));return X===-1&&(X=0),X}),k=(()=>{if(c==null)return;if(V===null)return{...c,inner:void 0};let D=Array.from(d.listRef.current.values());return{...c,inner:{listRef:{current:D},index:V}}})(),[G,ne]=Zt(k),F=Qt(),I=z(n,c?G:null,f.actions.setOptionsElement,p),U=Se();(0,oe.useEffect)(()=>{var X;let D=b;D&&m===0&&D!==((X=Pe(D))==null?void 0:X.activeElement)&&(D==null||D.focus({preventScroll:!0}))},[m,b]);let W=E(D=>{var X,re;switch(U.dispose(),D.key){case" ":if(f.state.searchQuery!=="")return D.preventDefault(),D.stopPropagation(),f.actions.search(D.key);case"Enter":if(D.preventDefault(),D.stopPropagation(),f.state.activeOptionIndex!==null){let{dataRef:je}=f.state.options[f.state.activeOptionIndex];f.actions.onChange(je.current.value)}d.mode===0&&((0,Yn.flushSync)(()=>f.actions.closeListbox()),(X=f.state.buttonElement)==null||X.focus({preventScroll:!0}));break;case q(d.orientation,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:2});case q(d.orientation,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:1});case"Home":case"PageUp":return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:0});case"End":case"PageDown":return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:3});case"Escape":D.preventDefault(),D.stopPropagation(),(0,Yn.flushSync)(()=>f.actions.closeListbox()),(re=f.state.buttonElement)==null||re.focus({preventScroll:!0});return;case"Tab":D.preventDefault(),D.stopPropagation(),(0,Yn.flushSync)(()=>f.actions.closeListbox()),sr(f.state.buttonElement,D.shiftKey?2:4);break;default:D.key.length===1&&(f.actions.search(D.key),U.setTimeout(()=>f.actions.clearSearch(),350));break}}),Y=ee(f,D=>{var X;return(X=D.buttonElement)==null?void 0:X.id}),K=(0,oe.useMemo)(()=>({open:m===0}),[m]),w=ae(c?F():{},{id:o,ref:I,"aria-activedescendant":ee(f,f.selectors.activeDescendantId),"aria-multiselectable":d.mode===1?!0:void 0,"aria-labelledby":Y,"aria-orientation":d.orientation,onKeyDown:W,role:"listbox",tabIndex:m===0?0:void 0,style:{...a.style,...ne,"--button-width":It(T,!0).width},...Qe(R)}),H=N(),M=(0,oe.useMemo)(()=>d.mode===1?d:{...d,isSelected:P},[d,P]);return oe.default.createElement(lt,{enabled:i?e.static||x:!1,ownerDocument:h},oe.default.createElement(_r.Provider,{value:M},H({ourProps:w,theirProps:a,slot:K,defaultTag:yp,features:hp,visible:C,name:"Listbox.Options"})))}var Ep="div";function xp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-listbox-option-${t}`,disabled:r=!1,value:i,...s}=e,l=(0,oe.useContext)(uu)===!0,a=$o("Listbox.Option"),c=wr("Listbox.Option"),u=ee(c,S=>c.selectors.isActive(S,o)),p=a.isSelected(i),d=(0,oe.useRef)(null),f=Fr(d),m=me({disabled:r,value:i,domRef:d,get textValue(){return f()}}),T=z(n,d,S=>{S?a.listRef.current.set(o,S):a.listRef.current.delete(o)}),b=ee(c,S=>c.selectors.shouldScrollIntoView(S,o));j(()=>{if(b)return he().requestAnimationFrame(()=>{var S,C;(C=(S=d.current)==null?void 0:S.scrollIntoView)==null||C.call(S,{block:"nearest"})})},[b,d]),j(()=>{if(!l)return c.actions.registerOption(o,m),()=>c.actions.unregisterOption(o)},[m,o,l]);let g=E(S=>{var C;if(r)return S.preventDefault();c.actions.onChange(i),a.mode===0&&((0,Yn.flushSync)(()=>c.actions.closeListbox()),(C=c.state.buttonElement)==null||C.focus({preventScroll:!0}))}),h=E(()=>{if(r)return c.actions.goToOption({focus:5});c.actions.goToOption({focus:4,id:o})}),y=Ln(),v=E(S=>y.update(S)),x=E(S=>{y.wasMoved(S)&&(r||u&&c.state.activationTrigger===0||c.actions.goToOption({focus:4,id:o},0))}),R=E(S=>{y.wasMoved(S)&&(r||u&&c.state.activationTrigger===0&&c.actions.goToOption({focus:5}))}),$=(0,oe.useMemo)(()=>({active:u,focus:u,selected:p,disabled:r,selectedOption:p&&l}),[u,p,r,l]),A=l?{}:{id:o,ref:T,role:"option",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-selected":p,disabled:void 0,onClick:g,onFocus:h,onPointerEnter:v,onMouseEnter:v,onPointerMove:x,onMouseMove:x,onPointerLeave:R,onMouseLeave:R},B=N();return!p&&l?null:B({ourProps:A,theirProps:s,slot:$,defaultTag:Ep,name:"Listbox.Option"})}var Pp=oe.Fragment;function Rp(e,n){let{options:t,placeholder:o,...r}=e,s={ref:z(n)},l=$o("ListboxSelectedOption"),a=(0,oe.useMemo)(()=>({}),[]),c=l.value===void 0||l.value===null||l.mode===1&&Array.isArray(l.value)&&l.value.length===0,u=N();return oe.default.createElement(uu.Provider,{value:!0},u({ourProps:s,theirProps:{...r,children:oe.default.createElement(oe.default.Fragment,null,o&&c?o:t)},slot:a,defaultTag:Pp,name:"ListboxSelectedOption"}))}var Sp=_(Tp),cu=_(gp),fu=nt,du=_(vp),pu=_(xp),mu=_(Rp),Cp=Object.assign(Sp,{Button:cu,Label:fu,Options:du,Option:pu,SelectedOption:mu});var de=le(require("react"),1),kr=require("react-dom");function Tu(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,o=Ve(n(e.items.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{items:o,activeItemIndex:r}}var Ap={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:5},menuState:1}},[0](e,n){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:n.focus,menuState:0}},[2]:(e,n)=>{var i,s,l,a,c;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeItemIndex:null};if(n.focus===4)return{...t,activeItemIndex:e.items.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=tt(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeItemIndex:d}}}}else if(n.focus===2){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=tt(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((a=p.current)==null?void 0:a.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeItemIndex:d}}}}let o=Tu(e),r=tt(n,{resolveItems:()=>o.items,resolveActiveIndex:()=>o.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeItemIndex:r}},[3]:(e,n)=>{let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+o).concat(e.items.slice(0,e.activeItemIndex+o)):e.items).find(a=>{var c;return((c=a.dataRef.current.textValue)==null?void 0:c.startsWith(r))&&!a.dataRef.current.disabled}),l=s?e.items.indexOf(s):-1;return l===-1||l===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:l,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=e.items.concat(n.items.map(r=>r)),o=e.activeItemIndex;return e.pendingFocus.focus!==5&&(o=tt(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled})),{...e,items:t,activeItemIndex:o,pendingFocus:{focus:5},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.items,o=[],r=new Set(n.items);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,items:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...Tu(e),pendingShouldSort:!1}:e},qn=class extends ot{constructor(t){super(t);Oe(this,"actions",{registerItem:ln(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:ln(()=>{let t=[];return[o=>t.push(o),()=>this.send({type:6,items:t.splice(0)})]})});Oe(this,"selectors",{activeDescendantId(t){var i;let o=t.activeItemIndex,r=t.items;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeItemIndex,i=t.items;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{this.disposables.requestAnimationFrame(()=>{this.send({type:9})})});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.menuState===0&&this.send({type:1})})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new qn({id:t,__demoMode:o,menuState:o?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:5}})}reduce(t,o){return q(o.type,Ap,t,o)}};var Jn=require("react");var es=(0,Jn.createContext)(null);function $r(e){let n=(0,Jn.useContext)(es);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ts),t}return n}function ts({id:e,__demoMode:n=!1}){let t=(0,Jn.useMemo)(()=>qn.new({id:e,__demoMode:n}),[]);return st(()=>t.dispose()),t}var Op=de.Fragment;function Dp(e,n){let t=(0,J.useId)(),{__demoMode:o=!1,...r}=e,i=ts({id:t,__demoMode:o}),[s,l,a]=ee(i,b=>[b.menuState,b.itemsElement,b.buttonElement]),c=z(n),u=$e.get(null),p=ee(u,(0,de.useCallback)(b=>u.selectors.isTop(b,t),[u,t]));Et(p,[a,l],(b,g)=>{var h;i.send({type:1}),Ft(g,1)||(b.preventDefault(),(h=i.state.buttonElement)==null||h.focus())});let d=E(()=>{i.send({type:1})}),f=(0,de.useMemo)(()=>({open:s===0,close:d}),[s,d]),m={ref:c},T=N();return de.default.createElement(en,null,de.default.createElement(es.Provider,{value:i},de.default.createElement(rt,{value:q(s,{[0]:1,[1]:2})},T({ourProps:m,theirProps:r,slot:f,defaultTag:Op,name:"Menu"}))))}var Lp="button";function Ip(e,n){let t=$r("Menu.Button"),o=(0,J.useId)(),{id:r=`headlessui-menu-button-${o}`,disabled:i=!1,autoFocus:s=!1,...l}=e,a=(0,de.useRef)(null),c=vr(),u=z(n,a,Jt(),E(P=>t.send({type:7,element:P}))),p=E(P=>{switch(P.key){case" ":case"Enter":case"ArrowDown":P.preventDefault(),P.stopPropagation(),t.send({type:0,focus:{focus:0}});break;case"ArrowUp":P.preventDefault(),P.stopPropagation(),t.send({type:0,focus:{focus:3}});break}}),d=E(P=>{switch(P.key){case" ":P.preventDefault();break}}),[f,m,T]=ee(t,P=>[P.menuState,P.buttonElement,P.itemsElement]),b=f===0;Dn(b,{trigger:m,action:(0,de.useCallback)(P=>{if(m!=null&&m.contains(P.target))return Ge.Ignore;let V=P.target.closest('[role="menuitem"]:not([data-disabled])');return Te(V)?Ge.Select(V):T!=null&&T.contains(P.target)?Ge.Ignore:Ge.Close},[m,T]),close:(0,de.useCallback)(()=>t.send({type:1}),[]),select:(0,de.useCallback)(P=>P.click(),[])});let g=E(P=>{var V;if(P.button===0){if(_e(P.currentTarget))return P.preventDefault();i||(f===0?((0,kr.flushSync)(()=>t.send({type:1})),(V=a.current)==null||V.focus({preventScroll:!0})):(P.preventDefault(),t.send({type:0,focus:{focus:5},trigger:0})))}}),h=(0,de.useRef)(null),y=E(P=>{h.current=P.pointerType,P.pointerType==="mouse"&&g(P)}),v=E(P=>{h.current!=="mouse"&&g(P)}),{isFocusVisible:x,focusProps:R}=ce({autoFocus:s}),{isHovered:$,hoverProps:A}=fe({isDisabled:i}),{pressed:B,pressProps:S}=Ce({disabled:i}),C=(0,de.useMemo)(()=>({open:f===0,active:B||f===0,disabled:i,hover:$,focus:x,autofocus:s}),[f,$,x,B,i,s]),L=ae(c(),{ref:u,id:r,type:Ke(e,a.current),"aria-haspopup":"menu","aria-controls":T==null?void 0:T.id,"aria-expanded":f===0,disabled:i||void 0,autoFocus:s,onKeyDown:p,onKeyUp:d,onPointerDown:y,onClick:v},R,A,S);return N()({ourProps:L,theirProps:l,slot:C,defaultTag:Lp,name:"Menu.Button"})}var Mp="div",Fp=3;function wp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-items-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,c=qt(r),u=$r("Menu.Items"),[p,d]=Zt(c),f=Qt(),[m,T]=(0,de.useState)(null),b=z(n,c?p:null,E(I=>u.send({type:8,element:I})),T),[g,h]=ee(u,I=>[I.menuState,I.buttonElement]),y=Re(h),v=Re(m);c&&(i=!0);let x=He(),[R,$]=Ze(l,m,x!==null?(x&1)===1:g===0);vt(R,h,()=>{u.send({type:1})});let A=ee(u,I=>I.__demoMode),B=A?!1:s&&g===0;xt(B,v);let S=A?!1:s&&g===0;Wt(S,{allowed:(0,de.useCallback)(()=>[h,m],[h,m])});let C=g!==0,O=Mr(C,h)?!1:R;(0,de.useEffect)(()=>{let I=m;I&&g===0&&I!==(v==null?void 0:v.activeElement)&&I.focus({preventScroll:!0})},[g,m,v]),ur(g===0,{container:m,accept(I){return I.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:I.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(I){I.setAttribute("role","none")}});let P=Se(),V=E(I=>{var U,W,Y;switch(P.dispose(),I.key){case" ":if(u.state.searchQuery!=="")return I.preventDefault(),I.stopPropagation(),u.send({type:3,value:I.key});case"Enter":if(I.preventDefault(),I.stopPropagation(),u.state.activeItemIndex!==null){let{dataRef:K}=u.state.items[u.state.activeItemIndex];(W=(U=K.current)==null?void 0:U.domRef.current)==null||W.click()}u.send({type:1}),mi(u.state.buttonElement);break;case"ArrowDown":return I.preventDefault(),I.stopPropagation(),u.send({type:2,focus:2});case"ArrowUp":return I.preventDefault(),I.stopPropagation(),u.send({type:2,focus:1});case"Home":case"PageUp":return I.preventDefault(),I.stopPropagation(),u.send({type:2,focus:0});case"End":case"PageDown":return I.preventDefault(),I.stopPropagation(),u.send({type:2,focus:3});case"Escape":I.preventDefault(),I.stopPropagation(),(0,kr.flushSync)(()=>u.send({type:1})),(Y=u.state.buttonElement)==null||Y.focus({preventScroll:!0});break;case"Tab":I.preventDefault(),I.stopPropagation(),(0,kr.flushSync)(()=>u.send({type:1})),sr(u.state.buttonElement,I.shiftKey?2:4);break;default:I.key.length===1&&(u.send({type:3,value:I.key}),P.setTimeout(()=>u.send({type:4}),350));break}}),k=E(I=>{switch(I.key){case" ":I.preventDefault();break}}),G=(0,de.useMemo)(()=>({open:g===0}),[g]),ne=ae(c?f():{},{"aria-activedescendant":ee(u,u.selectors.activeDescendantId),"aria-labelledby":ee(u,I=>{var U;return(U=I.buttonElement)==null?void 0:U.id}),id:o,onKeyDown:V,onKeyUp:k,role:"menu",tabIndex:g===0?0:void 0,ref:b,style:{...a.style,...d,"--button-width":It(h,!0).width},...Qe($)}),F=N();return de.default.createElement(lt,{enabled:i?e.static||R:!1,ownerDocument:y},F({ourProps:ne,theirProps:a,slot:G,defaultTag:Mp,features:Fp,visible:O,name:"Menu.Items"}))}var _p=de.Fragment;function $p(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-item-${t}`,disabled:r=!1,...i}=e,s=$r("Menu.Item"),l=ee(s,C=>s.selectors.isActive(C,o)),a=(0,de.useRef)(null),c=z(n,a),u=ee(s,C=>s.selectors.shouldScrollIntoView(C,o));j(()=>{if(u)return he().requestAnimationFrame(()=>{var C,L;(L=(C=a.current)==null?void 0:C.scrollIntoView)==null||L.call(C,{block:"nearest"})})},[u,a]);let p=Fr(a),d=(0,de.useRef)({disabled:r,domRef:a,get textValue(){return p()}});j(()=>{d.current.disabled=r},[d,r]),j(()=>(s.actions.registerItem(o,d),()=>s.actions.unregisterItem(o)),[d,o]);let f=E(()=>{s.send({type:1})}),m=E(C=>{if(r)return C.preventDefault();s.send({type:1}),mi(s.state.buttonElement)}),T=E(()=>{if(r)return s.send({type:2,focus:5});s.send({type:2,focus:4,id:o})}),b=Ln(),g=E(C=>b.update(C)),h=E(C=>{b.wasMoved(C)&&(r||l||s.send({type:2,focus:4,id:o,trigger:0}))}),y=E(C=>{b.wasMoved(C)&&(r||l&&s.state.activationTrigger===0&&s.send({type:2,focus:5}))}),[v,x]=Be(),[R,$]=ct(),A=(0,de.useMemo)(()=>({active:l,focus:l,disabled:r,close:f}),[l,r,f]),B={id:o,ref:c,role:"menuitem",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-labelledby":v,"aria-describedby":R,disabled:void 0,onClick:m,onFocus:T,onPointerEnter:g,onMouseEnter:g,onPointerMove:h,onMouseMove:h,onPointerLeave:y,onMouseLeave:y},S=N();return de.default.createElement(x,null,de.default.createElement($,null,S({ourProps:B,theirProps:i,slot:A,defaultTag:_p,name:"Menu.Item"})))}var kp="div";function Hp(e,n){let[t,o]=Be(),r=e,i={ref:n,"aria-labelledby":t,role:"group"},s=N();return de.default.createElement(o,null,s({ourProps:i,theirProps:r,slot:{},defaultTag:kp,name:"Menu.Section"}))}var Np="header";function Bp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-heading-${t}`,...r}=e,i=tr();j(()=>i.register(o),[o,i.register]);let s={id:o,ref:n,role:"presentation",...i.props};return N()({ourProps:s,theirProps:r,slot:{},defaultTag:Np,name:"Menu.Heading"})}var Gp="div";function Up(e,n){let t=e,o={ref:n,role:"separator"};return N()({ourProps:o,theirProps:t,slot:{},defaultTag:Gp,name:"Menu.Separator"})}var Vp=_(Dp),bu=_(Ip),gu=_(wp),yu=_($p),hu=_(Hp),vu=_(Bp),Eu=_(Up),Wp=Object.assign(Vp,{Button:bu,Items:gu,Item:yu,Section:hu,Heading:vu,Separator:Eu});var Q=le(require("react"),1);var jp={[0]:e=>e.popoverState===0?e:{...e,popoverState:0,__demoMode:!1},[1](e){return e.popoverState===1?e:{...e,popoverState:1,__demoMode:!1}},[2](e,n){return e.button===n.button?e:{...e,button:n.button}},[3](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[4](e,n){return e.panel===n.panel?e:{...e,panel:n.panel}},[5](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},Qn=class extends ot{constructor(t){super(t);Oe(this,"actions",{close:()=>this.send({type:1}),refocusableClose:t=>{this.actions.close();let o=(()=>t?Te(t)?t:"current"in t&&Te(t.current)?t.current:this.state.button:this.state.button)();o==null||o.focus()},open:()=>this.send({type:0}),setButtonId:t=>this.send({type:3,buttonId:t}),setButton:t=>this.send({type:2,button:t}),setPanelId:t=>this.send({type:5,panelId:t}),setPanel:t=>this.send({type:4,panel:t})});Oe(this,"selectors",{isPortalled:t=>{if(!t.button||!t.panel)return!1;for(let c of document.querySelectorAll("body > *"))if(Number(c==null?void 0:c.contains(t.button))^Number(c==null?void 0:c.contains(t.panel)))return!0;let o=an(),r=o.indexOf(t.button),i=(r+o.length-1)%o.length,s=(r+1)%o.length,l=o[i],a=o[s];return!t.panel.contains(l)&&!t.panel.contains(a)}});{let o=this.state.id,r=$e.get(null);this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new Qn({id:t,__demoMode:o,popoverState:o?0:1,buttons:{current:[]},button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:{current:null},afterPanelSentinel:{current:null},afterButtonSentinel:{current:null}})}reduce(t,o){return q(o.type,jp,t,o)}};var Zn=require("react");var ns=(0,Zn.createContext)(null);function ko(e){let n=(0,Zn.useContext)(ns);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ko),t}return n}function xu({id:e,__demoMode:n=!1}){let t=(0,Zn.useMemo)(()=>Qn.new({id:e,__demoMode:n}),[]);return st(()=>t.dispose()),t}var os=(0,Q.createContext)(null);os.displayName="PopoverGroupContext";function Pu(){return(0,Q.useContext)(os)}var Hr=(0,Q.createContext)(null);Hr.displayName="PopoverPanelContext";function Kp(){return(0,Q.useContext)(Hr)}var zp="div";function Xp(e,n){var O;let t=(0,J.useId)(),{__demoMode:o=!1,...r}=e,i=xu({id:t,__demoMode:o}),s=(0,Q.useRef)(null),l=z(n,Pn(P=>{s.current=P})),[a,c,u,p,d]=ee(i,(0,Q.useCallback)(P=>[P.popoverState,P.button,P.panel,P.buttonId,P.panelId],[])),f=Re((O=s.current)!=null?O:c),m=me(p),T=me(d),b=(0,Q.useMemo)(()=>({buttonId:m,panelId:T,close:i.actions.close}),[m,T,i]),g=Pu(),h=g==null?void 0:g.registerPopover,y=E(()=>{var P;return(P=g==null?void 0:g.isFocusWithinPopoverGroup())!=null?P:(f==null?void 0:f.activeElement)&&((c==null?void 0:c.contains(f.activeElement))||(u==null?void 0:u.contains(f.activeElement)))});(0,Q.useEffect)(()=>h==null?void 0:h(b),[h,b]);let[v,x]=Pr(),R=Io(c),$=Sr({mainTreeNode:R,portals:v,defaultContainers:[{get current(){return i.state.button}},{get current(){return i.state.panel}}]});Kt(f==null?void 0:f.defaultView,"focus",P=>{var V,k,G,ne,F,I;P.target!==window&&Le(P.target)&&i.state.popoverState===0&&(y()||i.state.button&&i.state.panel&&($.contains(P.target)||(k=(V=i.state.beforePanelSentinel.current)==null?void 0:V.contains)!=null&&k.call(V,P.target)||(ne=(G=i.state.afterPanelSentinel.current)==null?void 0:G.contains)!=null&&ne.call(G,P.target)||(I=(F=i.state.afterButtonSentinel.current)==null?void 0:F.contains)!=null&&I.call(F,P.target)||i.actions.close()))},!0);let A=a===0;Et(A,$.resolveContainers,(P,V)=>{i.actions.close(),Ft(V,1)||(P.preventDefault(),c==null||c.focus())});let B=(0,Q.useMemo)(()=>({open:a===0,close:i.actions.refocusableClose}),[a,i]),S=ee(i,(0,Q.useCallback)(P=>q(P.popoverState,{[0]:1,[1]:2}),[])),C={ref:l},L=N();return Q.default.createElement(Kn,{node:R},Q.default.createElement(en,null,Q.default.createElement(Hr.Provider,{value:null},Q.default.createElement(ns.Provider,{value:i},Q.default.createElement(rn,{value:i.actions.refocusableClose},Q.default.createElement(rt,{value:S},Q.default.createElement(x,null,L({ourProps:C,theirProps:r,slot:B,defaultTag:zp,name:"Popover"}))))))))}var Yp="button";function qp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,l=ko("Popover.Button"),[a,c,u,p,d,f,m]=ee(l,(0,Q.useCallback)(H=>[H.popoverState,l.selectors.isPortalled(H),H.button,H.buttonId,H.panel,H.panelId,H.afterButtonSentinel],[])),T=(0,Q.useRef)(null),b=`headlessui-focus-sentinel-${(0,J.useId)()}`,g=Pu(),h=g==null?void 0:g.closeOthers,v=Kp()!==null;(0,Q.useEffect)(()=>{if(!v)return l.actions.setButtonId(o),()=>l.actions.setButtonId(null)},[v,o,l]);let[x]=(0,Q.useState)(()=>Symbol()),R=z(T,n,Jt(),E(H=>{if(!v){if(H)l.state.buttons.current.push(x);else{let M=l.state.buttons.current.indexOf(x);M!==-1&&l.state.buttons.current.splice(M,1)}l.state.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),H&&l.actions.setButton(H)}})),$=z(T,n),A=Re(T),B=E(H=>{var M,D,X;if(v){if(l.state.popoverState===1)return;switch(H.key){case" ":case"Enter":H.preventDefault(),(D=(M=H.target).click)==null||D.call(M),l.actions.close(),(X=l.state.button)==null||X.focus();break}}else switch(H.key){case" ":case"Enter":H.preventDefault(),H.stopPropagation(),l.state.popoverState===1?(h==null||h(l.state.buttonId),l.actions.open()):l.actions.close();break;case"Escape":if(l.state.popoverState!==0)return h==null?void 0:h(l.state.buttonId);if(!T.current||A!=null&&A.activeElement&&!T.current.contains(A.activeElement))return;H.preventDefault(),H.stopPropagation(),l.actions.close();break}}),S=E(H=>{v||H.key===" "&&H.preventDefault()}),C=E(H=>{var M,D;_e(H.currentTarget)||r||(v?(l.actions.close(),(M=l.state.button)==null||M.focus()):(H.preventDefault(),H.stopPropagation(),l.state.popoverState===1?(h==null||h(l.state.buttonId),l.actions.open()):l.actions.close(),(D=l.state.button)==null||D.focus()))}),L=E(H=>{H.preventDefault(),H.stopPropagation()}),{isFocusVisible:O,focusProps:P}=ce({autoFocus:i}),{isHovered:V,hoverProps:k}=fe({isDisabled:r}),{pressed:G,pressProps:ne}=Ce({disabled:r}),F=a===0,I=(0,Q.useMemo)(()=>({open:F,active:G||F,disabled:r,hover:V,focus:O,autofocus:i}),[F,V,O,G,r,i]),U=Ke(e,u),W=v?ae({ref:$,type:U,onKeyDown:B,onClick:C,disabled:r||void 0,autoFocus:i},P,k,ne):ae({ref:R,id:p,type:U,"aria-expanded":a===0,"aria-controls":d?f:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:B,onKeyUp:S,onClick:C,onMouseDown:L},P,k,ne),Y=Mo(),K=E(()=>{if(!Te(l.state.panel))return;let H=l.state.panel;function M(){q(Y.current,{[0]:()=>ve(H,1),[1]:()=>ve(H,8)})===0&&ve(an().filter(X=>X.dataset.headlessuiFocusGuard!=="true"),q(Y.current,{[0]:4,[1]:2}),{relativeTo:l.state.button})}M()}),w=N();return Q.default.createElement(Q.default.Fragment,null,w({ourProps:W,theirProps:s,slot:I,defaultTag:Yp,name:"Popover.Button"}),F&&!v&&c&&Q.default.createElement(ke,{id:b,ref:m,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:K}))}var Jp="div",Qp=3;function Ru(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-backdrop-${t}`,transition:r=!1,...i}=e,s=ko("Popover.Backdrop"),l=ee(s,(0,Q.useCallback)(h=>h.popoverState,[])),[a,c]=(0,Q.useState)(null),u=z(n,c),p=He(),[d,f]=Ze(r,a,p!==null?(p&1)===1:l===0),m=E(h=>{if(_e(h.currentTarget))return h.preventDefault();s.actions.close()}),T=(0,Q.useMemo)(()=>({open:l===0}),[l]),b={ref:u,id:o,"aria-hidden":!0,onClick:m,...Qe(f)};return N()({ourProps:b,theirProps:i,slot:T,defaultTag:Jp,features:Qp,visible:d,name:"Popover.Backdrop"})}var Zp="div",em=3;function tm(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-panel-${t}`,focus:r=!1,anchor:i,portal:s=!1,modal:l=!1,transition:a=!1,...c}=e,u=ko("Popover.Panel"),p=ee(u,u.selectors.isPortalled),[d,f,m,T,b]=ee(u,(0,Q.useCallback)(K=>[K.popoverState,K.button,K.__demoMode,K.beforePanelSentinel,K.afterPanelSentinel],[])),g=`headlessui-focus-sentinel-before-${t}`,h=`headlessui-focus-sentinel-after-${t}`,y=(0,Q.useRef)(null),v=qt(i),[x,R]=Zt(v),$=Qt();v&&(s=!0);let[A,B]=(0,Q.useState)(null),S=z(y,n,v?x:null,u.actions.setPanel,B),C=Re(f),L=Re(y);j(()=>(u.actions.setPanelId(o),()=>u.actions.setPanelId(null)),[o,u]);let O=He(),[P,V]=Ze(a,A,O!==null?(O&1)===1:d===0);vt(P,f,u.actions.close),xt(m?!1:l&&P,L);let G=E(K=>{var w;switch(K.key){case"Escape":if(u.state.popoverState!==0||!y.current||L!=null&&L.activeElement&&!y.current.contains(L.activeElement))return;K.preventDefault(),K.stopPropagation(),u.actions.close(),(w=u.state.button)==null||w.focus();break}});(0,Q.useEffect)(()=>{var K;e.static||d===1&&((K=e.unmount)==null||K)&&u.actions.setPanel(null)},[d,e.unmount,e.static,u]),(0,Q.useEffect)(()=>{if(m||!r||d!==0||!y.current)return;let K=L==null?void 0:L.activeElement;y.current.contains(K)||ve(y.current,1)},[m,r,y.current,d]);let ne=(0,Q.useMemo)(()=>({open:d===0,close:u.actions.refocusableClose}),[d,u]),F=ae(v?$():{},{ref:S,id:o,onKeyDown:G,onBlur:r&&d===0?K=>{var H,M,D,X,re;let w=K.relatedTarget;w&&y.current&&((H=y.current)!=null&&H.contains(w)||(u.actions.close(),((D=(M=T.current)==null?void 0:M.contains)!=null&&D.call(M,w)||(re=(X=b.current)==null?void 0:X.contains)!=null&&re.call(X,w))&&w.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...c.style,...R,"--button-width":It(f,!0).width},...Qe(V)}),I=Mo(),U=E(()=>{let K=y.current;if(!K)return;function w(){q(I.current,{[0]:()=>{var M;ve(K,1)===0&&((M=u.state.afterPanelSentinel.current)==null||M.focus())},[1]:()=>{var H;(H=u.state.button)==null||H.focus({preventScroll:!0})}})}w()}),W=E(()=>{let K=y.current;if(!K)return;function w(){q(I.current,{[0]:()=>{if(!u.state.button)return;let H=an(),M=H.indexOf(u.state.button),D=H.slice(0,M+1),re=[...H.slice(M+1),...D];for(let je of re.slice())if(je.dataset.headlessuiFocusGuard==="true"||A!=null&&A.contains(je)){let ps=re.indexOf(je);ps!==-1&&re.splice(ps,1)}ve(re,1,{sorted:!1})},[1]:()=>{var M;ve(K,2)===0&&((M=u.state.button)==null||M.focus())}})}w()}),Y=N();return Q.default.createElement(Nn,null,Q.default.createElement(Hr.Provider,{value:o},Q.default.createElement(rn,{value:u.actions.refocusableClose},Q.default.createElement(lt,{enabled:s?e.static||P:!1,ownerDocument:C},P&&p&&Q.default.createElement(ke,{id:g,ref:T,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:U}),Y({ourProps:F,theirProps:c,slot:ne,defaultTag:Zp,features:em,visible:P,name:"Popover.Panel"}),P&&p&&Q.default.createElement(ke,{id:h,ref:b,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:W})))))}var nm="div";function om(e,n){let t=(0,Q.useRef)(null),o=z(t,n),[r,i]=(0,Q.useState)([]),s=E(T=>{i(b=>{let g=b.indexOf(T);if(g!==-1){let h=b.slice();return h.splice(g,1),h}return b})}),l=E(T=>(i(b=>[...b,T]),()=>s(T))),a=E(()=>{var g;let T=Pe(t);if(!T)return!1;let b=T.activeElement;return(g=t.current)!=null&&g.contains(b)?!0:r.some(h=>{var y,v;return((y=T.getElementById(h.buttonId.current))==null?void 0:y.contains(b))||((v=T.getElementById(h.panelId.current))==null?void 0:v.contains(b))})}),c=E(T=>{for(let b of r)b.buttonId.current!==T&&b.close()}),u=(0,Q.useMemo)(()=>({registerPopover:l,unregisterPopover:s,isFocusWithinPopoverGroup:a,closeOthers:c}),[l,s,a,c]),p=(0,Q.useMemo)(()=>({}),[]),d=e,f={ref:o},m=N();return Q.default.createElement(Kn,null,Q.default.createElement(os.Provider,{value:u},m({ourProps:f,theirProps:d,slot:p,defaultTag:nm,name:"Popover.Group"})))}var rm=_(Xp),Su=_(qp),Cu=_(Ru),Au=_(Ru),Ou=_(tm),Du=_(om),im=Object.assign(rm,{Button:Su,Backdrop:Au,Overlay:Cu,Panel:Ou,Group:Du});var pe=le(require("react"),1);var sm={[0](e,n){let t=[...e.options,{id:n.id,element:n.element,propsRef:n.propsRef}];return{...e,options:Ve(t,o=>o.element.current)}},[1](e,n){let t=e.options.slice(),o=e.options.findIndex(r=>r.id===n.id);return o===-1?e:(t.splice(o,1),{...e,options:t})}},rs=(0,pe.createContext)(null);rs.displayName="RadioGroupDataContext";function is(e){let n=(0,pe.useContext)(rs);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,is),t}return n}var ss=(0,pe.createContext)(null);ss.displayName="RadioGroupActionsContext";function ls(e){let n=(0,pe.useContext)(ss);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ls),t}return n}function lm(e,n){return q(n.type,sm,e,n)}var am="div";function um(e,n){let t=(0,J.useId)(),o=ge(),{id:r=`headlessui-radiogroup-${t}`,value:i,form:s,name:l,onChange:a,by:c,disabled:u=o||!1,defaultValue:p,tabIndex:d=0,...f}=e,m=Sn(c),[T,b]=(0,pe.useReducer)(lm,{options:[]}),g=T.options,[h,y]=Be(),[v,x]=ct(),R=(0,pe.useRef)(null),$=z(R,n),A=gt(p),[B,S]=bt(i,a,A),C=(0,pe.useMemo)(()=>g.find(W=>!W.propsRef.current.disabled),[g]),L=(0,pe.useMemo)(()=>g.some(W=>m(W.propsRef.current.value,B)),[g,B]),O=E(W=>{var K;if(u||m(W,B))return!1;let Y=(K=g.find(w=>m(w.propsRef.current.value,W)))==null?void 0:K.propsRef.current;return Y!=null&&Y.disabled?!1:(S==null||S(W),!0)}),P=E(W=>{let Y=R.current;if(!Y)return;let K=Pe(Y),w=g.filter(H=>H.propsRef.current.disabled===!1).map(H=>H.element.current);switch(W.key){case"Enter":Ut(W.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(W.preventDefault(),W.stopPropagation(),ve(w,18)===2){let M=g.find(D=>D.element.current===(K==null?void 0:K.activeElement));M&&O(M.propsRef.current.value)}break;case"ArrowRight":case"ArrowDown":if(W.preventDefault(),W.stopPropagation(),ve(w,20)===2){let M=g.find(D=>D.element.current===(K==null?void 0:K.activeElement));M&&O(M.propsRef.current.value)}break;case" ":{W.preventDefault(),W.stopPropagation();let H=g.find(M=>M.element.current===(K==null?void 0:K.activeElement));H&&O(H.propsRef.current.value)}break}}),V=E(W=>(b({type:0,...W}),()=>b({type:1,id:W.id}))),k=(0,pe.useMemo)(()=>({value:B,firstOption:C,containsCheckedOption:L,disabled:u,compare:m,tabIndex:d,...T}),[B,C,L,u,m,d,T]),G=(0,pe.useMemo)(()=>({registerOption:V,change:O}),[V,O]),ne={ref:$,id:r,role:"radiogroup","aria-labelledby":h,"aria-describedby":v,onKeyDown:P},F=(0,pe.useMemo)(()=>({value:B}),[B]),I=(0,pe.useCallback)(()=>{if(A!==void 0)return O(A)},[O,A]),U=N();return pe.default.createElement(x,{name:"RadioGroup.Description"},pe.default.createElement(y,{name:"RadioGroup.Label"},pe.default.createElement(ss.Provider,{value:G},pe.default.createElement(rs.Provider,{value:k},l!=null&&pe.default.createElement(yt,{disabled:u,data:{[l]:B||"on"},overrides:{type:"radio",checked:B!=null},form:s,onReset:I}),U({ourProps:ne,theirProps:f,slot:F,defaultTag:am,name:"RadioGroup"})))))}var cm="div";function fm(e,n){var C;let t=is("RadioGroup.Option"),o=ls("RadioGroup.Option"),r=(0,J.useId)(),{id:i=`headlessui-radiogroup-option-${r}`,value:s,disabled:l=t.disabled||!1,autoFocus:a=!1,...c}=e,u=(0,pe.useRef)(null),p=z(u,n),[d,f]=Be(),[m,T]=ct(),b=me({value:s,disabled:l});j(()=>o.registerOption({id:i,element:u,propsRef:b}),[i,o,u,b]);let g=E(L=>{var O;if(_e(L.currentTarget))return L.preventDefault();o.change(s)&&((O=u.current)==null||O.focus())}),h=((C=t.firstOption)==null?void 0:C.id)===i,{isFocusVisible:y,focusProps:v}=ce({autoFocus:a}),{isHovered:x,hoverProps:R}=fe({isDisabled:l}),$=t.compare(t.value,s),A=ae({ref:p,id:i,role:"radio","aria-checked":$?"true":"false","aria-labelledby":d,"aria-describedby":m,"aria-disabled":l?!0:void 0,tabIndex:(()=>l?-1:$||!t.containsCheckedOption&&h?t.tabIndex:-1)(),onClick:l?void 0:g,autoFocus:a},v,R),B=(0,pe.useMemo)(()=>({checked:$,disabled:l,active:y,hover:x,focus:y,autofocus:a}),[$,l,x,y,a]),S=N();return pe.default.createElement(T,{name:"RadioGroup.Description"},pe.default.createElement(f,{name:"RadioGroup.Label"},S({ourProps:A,theirProps:c,slot:B,defaultTag:cm,name:"RadioGroup.Option"})))}var dm="span";function pm(e,n){var C;let t=is("Radio"),o=ls("Radio"),r=(0,J.useId)(),i=we(),s=ge(),{id:l=i||`headlessui-radio-${r}`,value:a,disabled:c=t.disabled||s||!1,autoFocus:u=!1,...p}=e,d=(0,pe.useRef)(null),f=z(d,n),m=Fe(),T=Ne(),b=me({value:a,disabled:c});j(()=>o.registerOption({id:l,element:d,propsRef:b}),[l,o,d,b]);let g=E(L=>{var O;if(_e(L.currentTarget))return L.preventDefault();o.change(a)&&((O=d.current)==null||O.focus())}),{isFocusVisible:h,focusProps:y}=ce({autoFocus:u}),{isHovered:v,hoverProps:x}=fe({isDisabled:c}),R=((C=t.firstOption)==null?void 0:C.id)===l,$=t.compare(t.value,a),A=ae({ref:f,id:l,role:"radio","aria-checked":$?"true":"false","aria-labelledby":m,"aria-describedby":T,"aria-disabled":c?!0:void 0,tabIndex:(()=>c?-1:$||!t.containsCheckedOption&&R?t.tabIndex:-1)(),autoFocus:u,onClick:c?void 0:g},y,x),B=(0,pe.useMemo)(()=>({checked:$,disabled:c,hover:v,focus:h,autofocus:u}),[$,c,v,h,u]);return N()({ourProps:A,theirProps:p,slot:B,defaultTag:dm,name:"Radio"})}var mm=_(um),Lu=_(fm),Iu=_(pm),Mu=nt,Fu=Lt,Tm=Object.assign(mm,{Option:Lu,Radio:Iu,Label:Mu,Description:Fu});var wu=require("react");var bm="select";function gm(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-select-${t}`,disabled:s=r||!1,invalid:l=!1,autoFocus:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocusVisible:d,focusProps:f}=ce({autoFocus:a}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),{pressed:b,pressProps:g}=Ce({disabled:s}),h=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":l?"true":void 0,disabled:s||void 0,autoFocus:a},f,T,g),y=(0,wu.useMemo)(()=>({disabled:s,invalid:l,hover:m,focus:d,active:b,autofocus:a}),[s,l,m,d,b,a]);return N()({ourProps:h,theirProps:c,slot:y,defaultTag:bm,name:"Select"})}var ym=_(gm);var xe=le(require("react"),1);var as=(0,xe.createContext)(null);as.displayName="GroupContext";var hm=xe.Fragment;function vm(e){var p;let[n,t]=(0,xe.useState)(null),[o,r]=Be(),[i,s]=ct(),l=(0,xe.useMemo)(()=>({switch:n,setSwitch:t}),[n,t]),a={},c=e,u=N();return xe.default.createElement(s,{name:"Switch.Description",value:i},xe.default.createElement(r,{name:"Switch.Label",value:o,props:{htmlFor:(p=l.switch)==null?void 0:p.id,onClick(d){n&&(po(d.currentTarget)&&d.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},xe.default.createElement(as.Provider,{value:l},u({ourProps:a,theirProps:c,slot:{},defaultTag:hm,name:"Switch.Group"}))))}var Em="button";function xm(e,n){var H;let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-switch-${t}`,disabled:s=r||!1,checked:l,defaultChecked:a,onChange:c,name:u,value:p,form:d,autoFocus:f=!1,...m}=e,T=(0,xe.useContext)(as),[b,g]=(0,xe.useState)(null),h=(0,xe.useRef)(null),y=z(h,n,T===null?null:T.setSwitch,g),v=gt(a),[x,R]=bt(l,c,v!=null?v:!1),$=Se(),[A,B]=(0,xe.useState)(!1),S=E(()=>{B(!0),R==null||R(!x),$.nextFrame(()=>{B(!1)})}),C=E(M=>{if(_e(M.currentTarget))return M.preventDefault();M.preventDefault(),S()}),L=E(M=>{M.key===" "?(M.preventDefault(),S()):M.key==="Enter"&&Ut(M.currentTarget)}),O=E(M=>M.preventDefault()),P=Fe(),V=Ne(),{isFocusVisible:k,focusProps:G}=ce({autoFocus:f}),{isHovered:ne,hoverProps:F}=fe({isDisabled:s}),{pressed:I,pressProps:U}=Ce({disabled:s}),W=(0,xe.useMemo)(()=>({checked:x,disabled:s,hover:ne,focus:k,active:I,autofocus:f,changing:A}),[x,ne,k,I,s,A,f]),Y=ae({id:i,ref:y,role:"switch",type:Ke(e,b),tabIndex:e.tabIndex===-1?0:(H=e.tabIndex)!=null?H:0,"aria-checked":x,"aria-labelledby":P,"aria-describedby":V,disabled:s||void 0,autoFocus:f,onClick:C,onKeyUp:L,onKeyPress:O},G,F,U),K=(0,xe.useCallback)(()=>{if(v!==void 0)return R==null?void 0:R(v)},[R,v]),w=N();return xe.default.createElement(xe.default.Fragment,null,u!=null&&xe.default.createElement(yt,{disabled:s,data:{[u]:p||"on"},overrides:{type:"checkbox",checked:x},form:d,onReset:K}),w({ourProps:Y,theirProps:m,slot:W,defaultTag:Em,name:"Switch"}))}var Pm=_(xm),_u=vm,$u=nt,ku=Lt,Rm=Object.assign(Pm,{Group:_u,Label:$u,Description:ku});var be=le(require("react"),1);var Nr=le(require("react"),1);function Hu({onFocus:e}){let[n,t]=(0,Nr.useState)(!0),o=gn();return n?Nr.default.createElement(ke,{as:"button",type:"button",features:2,onFocus:r=>{r.preventDefault();let i,s=50;function l(){if(s--<=0){i&&cancelAnimationFrame(i);return}if(e()){if(cancelAnimationFrame(i),!o.current)return;t(!1);return}i=requestAnimationFrame(l)}i=requestAnimationFrame(l)}}):null}var at=le(require("react"),1),Nu=at.createContext(null);function Sm(){return{groups:new Map,get(e,n){var s;let t=this.groups.get(e);t||(t=new Map,this.groups.set(e,t));let o=(s=t.get(n))!=null?s:0;t.set(n,o+1);let r=Array.from(t.keys()).indexOf(n);function i(){let l=t.get(n);l>1?t.set(n,l-1):t.delete(n)}return[r,i]}}}function Bu({children:e}){let n=at.useRef(Sm());return at.createElement(Nu.Provider,{value:n},e)}function us(e){let n=at.useContext(Nu);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=at.useId(),[o,r]=n.current.get(e,t);return at.useEffect(()=>r,[]),o}var Cm={[0](e,n){var u;let t=Ve(e.tabs,p=>p.current),o=Ve(e.panels,p=>p.current),r=t.filter(p=>{var d;return!((d=p.current)!=null&&d.hasAttribute("disabled"))}),i={...e,tabs:t,panels:o};if(n.index<0||n.index>t.length-1){let p=q(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>q(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(r.length===0)return i;let d=q(p,{[0]:()=>t.indexOf(r[0]),[1]:()=>t.indexOf(r[r.length-1])});return{...i,selectedIndex:d===-1?e.selectedIndex:d}}let s=t.slice(0,n.index),a=[...t.slice(n.index),...s].find(p=>r.includes(p));if(!a)return i;let c=(u=t.indexOf(a))!=null?u:e.selectedIndex;return c===-1&&(c=e.selectedIndex),{...i,selectedIndex:c}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],o=Ve([...e.tabs,n.tab],i=>i.current),r=e.selectedIndex;return e.info.current.isControlled||(r=o.indexOf(t),r===-1&&(r=e.selectedIndex)),{...e,tabs:o,selectedIndex:r}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:Ve([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},cs=(0,be.createContext)(null);cs.displayName="TabsDataContext";function eo(e){let n=(0,be.useContext)(cs);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eo),t}return n}var fs=(0,be.createContext)(null);fs.displayName="TabsActionsContext";function ds(e){let n=(0,be.useContext)(fs);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ds),t}return n}function Am(e,n){return q(n.type,Cm,e,n)}var Om="div";function Dm(e,n){let{defaultIndex:t=0,vertical:o=!1,manual:r=!1,onChange:i,selectedIndex:s=null,...l}=e,a=o?"vertical":"horizontal",c=r?"manual":"auto",u=s!==null,p=me({isControlled:u}),d=z(n),[f,m]=(0,be.useReducer)(Am,{info:p,selectedIndex:s!=null?s:t,tabs:[],panels:[]}),T=(0,be.useMemo)(()=>({selectedIndex:f.selectedIndex}),[f.selectedIndex]),b=me(i||(()=>{})),g=me(f.tabs),h=(0,be.useMemo)(()=>({orientation:a,activation:c,...f}),[a,c,f]),y=E(S=>(m({type:1,tab:S}),()=>m({type:2,tab:S}))),v=E(S=>(m({type:3,panel:S}),()=>m({type:4,panel:S}))),x=E(S=>{R.current!==S&&b.current(S),u||m({type:0,index:S})}),R=me(u?e.selectedIndex:f.selectedIndex),$=(0,be.useMemo)(()=>({registerTab:y,registerPanel:v,change:x}),[]);j(()=>{m({type:0,index:s!=null?s:t})},[s]),j(()=>{if(R.current===void 0||f.tabs.length<=0)return;let S=Ve(f.tabs,L=>L.current);S.some((L,O)=>f.tabs[O]!==L)&&x(S.indexOf(f.tabs[R.current]))});let A={ref:d},B=N();return be.default.createElement(Bu,null,be.default.createElement(fs.Provider,{value:$},be.default.createElement(cs.Provider,{value:h},h.tabs.length<=0&&be.default.createElement(Hu,{onFocus:()=>{var S,C;for(let L of g.current)if(((S=L.current)==null?void 0:S.tabIndex)===0)return(C=L.current)==null||C.focus(),!0;return!1}}),B({ourProps:A,theirProps:l,slot:T,defaultTag:Om,name:"Tabs"}))))}var Lm="div";function Im(e,n){let{orientation:t,selectedIndex:o}=eo("Tab.List"),r=z(n),i=(0,be.useMemo)(()=>({selectedIndex:o}),[o]),s=e,l={ref:r,role:"tablist","aria-orientation":t};return N()({ourProps:l,theirProps:s,slot:i,defaultTag:Lm,name:"Tabs.List"})}var Mm="button";function Fm(e,n){var F,I;let t=(0,J.useId)(),{id:o=`headlessui-tabs-tab-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,{orientation:l,activation:a,selectedIndex:c,tabs:u,panels:p}=eo("Tab"),d=ds("Tab"),f=eo("Tab"),[m,T]=(0,be.useState)(null),b=(0,be.useRef)(null),g=z(b,n,T);j(()=>d.registerTab(b),[d,b]);let h=us("tabs"),y=u.indexOf(b);y===-1&&(y=h);let v=y===c,x=E(U=>{var Y;let W=U();if(W===2&&a==="auto"){let K=(Y=Pe(b))==null?void 0:Y.activeElement,w=f.tabs.findIndex(H=>H.current===K);w!==-1&&d.change(w)}return W}),R=E(U=>{let W=u.map(K=>K.current).filter(Boolean);if(U.key===" "||U.key==="Enter"){U.preventDefault(),U.stopPropagation(),d.change(y);return}switch(U.key){case"Home":case"PageUp":return U.preventDefault(),U.stopPropagation(),x(()=>ve(W,1));case"End":case"PageDown":return U.preventDefault(),U.stopPropagation(),x(()=>ve(W,8))}if(x(()=>q(l,{vertical(){return U.key==="ArrowUp"?ve(W,18):U.key==="ArrowDown"?ve(W,20):0},horizontal(){return U.key==="ArrowLeft"?ve(W,18):U.key==="ArrowRight"?ve(W,20):0}}))===2)return U.preventDefault()}),$=(0,be.useRef)(!1),A=E(()=>{var U;$.current||($.current=!0,(U=b.current)==null||U.focus({preventScroll:!0}),d.change(y),Dt(()=>{$.current=!1}))}),B=E(U=>{U.preventDefault()}),{isFocusVisible:S,focusProps:C}=ce({autoFocus:i}),{isHovered:L,hoverProps:O}=fe({isDisabled:r}),{pressed:P,pressProps:V}=Ce({disabled:r}),k=(0,be.useMemo)(()=>({selected:v,hover:L,active:P,focus:S,autofocus:i,disabled:r}),[v,L,S,P,i,r]),G=ae({ref:g,onKeyDown:R,onMouseDown:B,onClick:A,id:o,role:"tab",type:Ke(e,m),"aria-controls":(I=(F=p[y])==null?void 0:F.current)==null?void 0:I.id,"aria-selected":v,tabIndex:v?0:-1,disabled:r||void 0,autoFocus:i},C,O,V);return N()({ourProps:G,theirProps:s,slot:k,defaultTag:Mm,name:"Tabs.Tab"})}var wm="div";function _m(e,n){let{selectedIndex:t}=eo("Tab.Panels"),o=z(n),r=(0,be.useMemo)(()=>({selectedIndex:t}),[t]),i=e,s={ref:o};return N()({ourProps:s,theirProps:i,slot:r,defaultTag:wm,name:"Tabs.Panels"})}var $m="div",km=3;function Hm(e,n){var v,x,R,$;let t=(0,J.useId)(),{id:o=`headlessui-tabs-panel-${t}`,tabIndex:r=0,...i}=e,{selectedIndex:s,tabs:l,panels:a}=eo("Tab.Panel"),c=ds("Tab.Panel"),u=(0,be.useRef)(null),p=z(u,n);j(()=>c.registerPanel(u),[c,u]);let d=us("panels"),f=a.indexOf(u);f===-1&&(f=d);let m=f===s,{isFocusVisible:T,focusProps:b}=ce(),g=(0,be.useMemo)(()=>({selected:m,focus:T}),[m,T]),h=ae({ref:p,id:o,role:"tabpanel","aria-labelledby":(x=(v=l[f])==null?void 0:v.current)==null?void 0:x.id,tabIndex:m?r:-1},b),y=N();return!m&&((R=i.unmount)==null||R)&&!(($=i.static)!=null&&$)?be.default.createElement(ke,{"aria-hidden":"true",...h}):y({ourProps:h,theirProps:i,slot:g,defaultTag:$m,features:km,visible:m,name:"Tabs.Panel"})}var Nm=_(Fm),Gu=_(Dm),Uu=_(Im),Vu=_(_m),Wu=_(Hm),Bm=Object.assign(Nm,{Group:Gu,List:Uu,Panels:Vu,Panel:Wu});var ju=require("react");var Gm="textarea";function Um(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-textarea-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocused:d,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,ju.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:d,autofocus:l}),[s,a,m,d,l]);return N()({ourProps:b,theirProps:c,slot:g,defaultTag:Gm,name:"Textarea"})}var Vm=_(Um);
