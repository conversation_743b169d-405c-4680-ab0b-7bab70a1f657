/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M12.7 3H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H20a2 2 0 0 0 2-2v-4.7",
      key: "wjb7ig"
    }
  ],
  ["circle", { cx: "19", cy: "6", r: "3", key: "108a5v" }]
];
const MessageSquareDot = createLucideIcon("message-square-dot", __iconNode);

export { __iconNode, MessageSquareDot as default };
//# sourceMappingURL=message-square-dot.js.map
