{"version": 3, "sources": ["../../src/server/next-server.ts"], "sourcesContent": ["import './node-environment'\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { CacheFs } from '../shared/lib/utils'\nimport {\n  DecodeError,\n  PageNotFoundError,\n  MiddlewareNotFoundError,\n} from '../shared/lib/utils'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport type RenderResult from './render-result'\nimport type { FetchEventResult } from './web/types'\nimport type { PrerenderManifest, RoutesManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { ParsedUrl } from '../shared/lib/router/utils/parse-url'\nimport type { CacheControl } from './lib/cache-control'\nimport type { WaitUntil } from './after/builtin-request-context'\n\nimport fs from 'fs'\nimport { join, relative } from 'path'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport {\n  PAGES_MANIFEST,\n  BUILD_ID_FILE,\n  MIDDLEWARE_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n  CLIENT_PUBLIC_FILES_PATH,\n  APP_PATHS_MANIFEST,\n  SERVER_DIRECTORY,\n  NEXT_FONT_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport { findDir } from '../lib/find-pages-dir'\nimport { NodeNextRequest, NodeNextResponse } from './base-http/node'\nimport { sendRenderResult } from './send-payload'\nimport { parseUrl } from '../shared/lib/router/utils/parse-url'\nimport * as Log from '../build/output/log'\n\nimport type {\n  Options,\n  FindComponentsResult,\n  MiddlewareRoutingItem,\n  RequestContext,\n  NormalizedRouteManifest,\n  LoadedRenderOpts,\n  RouteHandler,\n  NextEnabledDirectories,\n  BaseRequestHandler,\n} from './base-server'\nimport BaseServer from './base-server'\nimport { getMaybePagePath, getPagePath } from './require'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { loadComponents } from './load-components'\nimport type { LoadComponentsReturnType } from './load-components'\nimport isError, { getProperError } from '../lib/is-error'\nimport { splitCookiesString, toNodeOutgoingHttpHeaders } from './web/utils'\nimport { getMiddlewareRouteMatcher } from '../shared/lib/router/utils/middleware-route-matcher'\nimport { loadEnvConfig } from '@next/env'\nimport { urlQueryToSearchParams } from '../shared/lib/router/utils/querystring'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport { getCloneableBody } from './body-streams'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport ResponseCache, {\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\n\nimport { isPagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { MatchOptions } from './route-matcher-managers/route-matcher-manager'\nimport { BubbledError, getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { nodeFs } from './lib/node-fs-methods'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport { signalFromNodeResponse } from './web/spec-extension/adapters/next-request'\nimport { loadManifest } from './load-manifest.external'\nimport { lazyRenderAppPage } from './route-modules/app-page/module.render'\nimport { lazyRenderPagesPage } from './route-modules/pages/module.render'\nimport { interopDefault } from '../lib/interop-default'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { RouteKind } from './route-kind'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { AwaiterOnce } from './after/awaiter'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\nimport { initializeCacheHandlers, setCacheHandler } from './use-cache/handlers'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { isPostpone } from './lib/router-utils/is-postpone'\nimport { NodeModuleLoader } from './lib/module-loader/node-module-loader'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport {\n  ensureInstrumentationRegistered,\n  getInstrumentationModule,\n} from './lib/router-utils/instrumentation-globals.external'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from './lib/router-utils/router-server-context'\n\nexport * from './base-server'\n\ndeclare const __non_webpack_require__: NodeRequire\n\n// For module that can be both CJS or ESM\nconst dynamicImportEsmDefault = process.env.NEXT_MINIMAL\n  ? (id: string) =>\n      import(/* webpackIgnore: true */ id).then((mod) => mod.default || mod)\n  : (id: string) => import(id).then((mod) => mod.default || mod)\n\nexport type NodeRequestHandler = BaseRequestHandler<\n  IncomingMessage | NodeNextRequest,\n  ServerResponse | NodeNextResponse\n>\n\ntype NodeRouteHandler = RouteHandler<NodeNextRequest, NodeNextResponse>\n\nconst MiddlewareMatcherCache = new WeakMap<\n  MiddlewareManifest['middleware'][string],\n  MiddlewareRouteMatch\n>()\n\nfunction getMiddlewareMatcher(\n  info: MiddlewareManifest['middleware'][string]\n): MiddlewareRouteMatch {\n  const stored = MiddlewareMatcherCache.get(info)\n  if (stored) {\n    return stored\n  }\n\n  if (!Array.isArray(info.matchers)) {\n    throw new Error(\n      `Invariant: invalid matchers for middleware ${JSON.stringify(info)}`\n    )\n  }\n\n  const matcher = getMiddlewareRouteMatcher(info.matchers)\n  MiddlewareMatcherCache.set(info, matcher)\n  return matcher\n}\n\nfunction installProcessErrorHandlers(\n  shouldRemoveUncaughtErrorAndRejectionListeners: boolean\n) {\n  // The conventional wisdom of Node.js and other runtimes is to treat\n  // unhandled errors as fatal and exit the process.\n  //\n  // But Next.js is not a generic JS runtime — it's a specialized runtime for\n  // React Server Components.\n  //\n  // Many unhandled rejections are due to the late-awaiting pattern for\n  // prefetching data. In Next.js it's OK to call an async function without\n  // immediately awaiting it, to start the request as soon as possible\n  // without blocking unncessarily on the result. These can end up\n  // triggering an \"unhandledRejection\" if it later turns out that the\n  // data is not needed to render the page. Example:\n  //\n  //     const promise = fetchData()\n  //     const shouldShow = await checkCondition()\n  //     if (shouldShow) {\n  //       return <Component promise={promise} />\n  //     }\n  //\n  // In this example, `fetchData` is called immediately to start the request\n  // as soon as possible, but if `shouldShow` is false, then it will be\n  // discarded without unwrapping its result. If it errors, it will trigger\n  // an \"unhandledRejection\" event.\n  //\n  // Ideally, we would suppress these rejections completely without warning,\n  // because we don't consider them real errors. (TODO: Currently we do warn.)\n  //\n  // But regardless of whether we do or don't warn, we definitely shouldn't\n  // crash the entire process.\n  //\n  // Even a \"legit\" unhandled error unrelated to prefetching shouldn't\n  // prevent the rest of the page from rendering.\n  //\n  // So, we're going to intentionally override the default error handling\n  // behavior of the outer JS runtime to be more forgiving\n\n  // Remove any existing \"unhandledRejection\" and \"uncaughtException\" handlers.\n  // This is gated behind an experimental flag until we've considered the impact\n  // in various deployment environments. It's possible this may always need to\n  // be configurable.\n  if (shouldRemoveUncaughtErrorAndRejectionListeners) {\n    process.removeAllListeners('uncaughtException')\n    process.removeAllListeners('unhandledRejection')\n  }\n\n  // Install a new handler to prevent the process from crashing.\n  process.on('unhandledRejection', (reason: unknown) => {\n    if (isPostpone(reason)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    // Immediately log the error.\n    // TODO: Ideally, if we knew that this error was triggered by application\n    // code, we would suppress it entirely without logging. We can't reliably\n    // detect all of these, but when dynamicIO is enabled, we could suppress\n    // at least some of them by waiting to log the error until after all in-\n    // progress renders have completed. Then, only log errors for which there\n    // was not a corresponding \"rejectionHandled\" event.\n    console.error(reason)\n  })\n\n  process.on('rejectionHandled', () => {\n    // TODO: See note in the unhandledRejection handler above. In the future,\n    // we may use the \"rejectionHandled\" event to de-queue an error from\n    // being logged.\n  })\n\n  // Unhandled exceptions are errors triggered by non-async functions, so this\n  // is unrelated to the late-awaiting pattern. However, for similar reasons,\n  // we still shouldn't crash the process. Just log it.\n  process.on('uncaughtException', (reason: unknown) => {\n    if (isPostpone(reason)) {\n      return\n    }\n    console.error(reason)\n  })\n}\n\nexport default class NextNodeServer extends BaseServer<\n  Options,\n  NodeNextRequest,\n  NodeNextResponse\n> {\n  protected middlewareManifestPath: string\n  private _serverDistDir: string | undefined\n  private imageResponseCache?: ResponseCache\n  private registeredInstrumentation: boolean = false\n  protected renderWorkersPromises?: Promise<void>\n  protected dynamicRoutes?: {\n    match: import('../shared/lib/router/utils/route-matcher').RouteMatchFn\n    page: string\n    re: RegExp\n  }[]\n  private routerServerHandler?: (\n    req: IncomingMessage,\n    res: ServerResponse\n  ) => void\n\n  protected cleanupListeners = new AsyncCallbackSet()\n  protected internalWaitUntil: WaitUntil | undefined\n  private isDev: boolean\n  private sriEnabled: boolean\n\n  constructor(options: Options) {\n    // Initialize super class\n    super(options)\n\n    const isDev = options.dev ?? false\n    this.isDev = isDev\n    this.sriEnabled = Boolean(options.conf.experimental?.sri?.algorithm)\n\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (this.renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    if (this.renderOpts.nextScriptWorkers) {\n      process.env.__NEXT_SCRIPT_WORKERS = JSON.stringify(true)\n    }\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.experimental.useSkewCookie\n      ? ''\n      : this.nextConfig.deploymentId || ''\n\n    if (!this.minimalMode) {\n      this.imageResponseCache = new ResponseCache(this.minimalMode)\n    }\n\n    const { appDocumentPreloading } = this.nextConfig.experimental\n    const isDefaultEnabled = typeof appDocumentPreloading === 'undefined'\n\n    if (\n      !options.dev &&\n      (appDocumentPreloading === true ||\n        !(this.minimalMode && isDefaultEnabled))\n    ) {\n      // pre-warm _document and _app as these will be\n      // needed for most requests\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_document',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_app',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    if (\n      !options.dev &&\n      !this.minimalMode &&\n      this.nextConfig.experimental.preloadEntriesOnStart\n    ) {\n      this.unstable_preloadEntries()\n    }\n\n    if (!options.dev) {\n      const { dynamicRoutes = [] } = this.getRoutesManifest() ?? {}\n      this.dynamicRoutes = dynamicRoutes.map((r) => {\n        // TODO: can we just re-use the regex from the manifest?\n        const regex = getRouteRegex(r.page)\n        const match = getRouteMatcher(regex)\n\n        return {\n          match,\n          page: r.page,\n          re: regex.re,\n        }\n      })\n    }\n\n    // ensure options are set when loadConfig isn't called\n    setHttpClientAndAgentOptions(this.nextConfig)\n\n    // Intercept fetch and other testmode apis.\n    if (this.serverOptions.experimentalTestProxy) {\n      process.env.NEXT_PRIVATE_TEST_PROXY = 'true'\n      const { interceptTestApis } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server') as typeof import('../experimental/testmode/server')\n      interceptTestApis()\n    }\n\n    this.middlewareManifestPath = join(this.serverDistDir, MIDDLEWARE_MANIFEST)\n\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause a unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    if (!options.dev) {\n      this.prepare().catch((err) => {\n        console.error('Failed to prepare server', err)\n      })\n    }\n\n    // when using compile mode static env isn't inlined so we\n    // need to populate in normal runtime env\n    if (this.renderOpts.isExperimentalCompile) {\n      populateStaticEnv(this.nextConfig)\n    }\n\n    const shouldRemoveUncaughtErrorAndRejectionListeners = Boolean(\n      options.conf.experimental?.removeUncaughtErrorAndRejectionListeners\n    )\n    installProcessErrorHandlers(shouldRemoveUncaughtErrorAndRejectionListeners)\n  }\n\n  public async unstable_preloadEntries(): Promise<void> {\n    // Ensure prepare process will be finished before preloading entries.\n    await this.prepare()\n\n    const appPathsManifest = this.getAppPathsManifest()\n    const pagesManifest = this.getPagesManifest()\n\n    await this.loadCustomCacheHandlers()\n\n    for (const page of Object.keys(pagesManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    for (const page of Object.keys(appPathsManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: true,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      })\n        .then(async ({ ComponentMod }) => {\n          // we need to ensure fetch is patched before we require the page,\n          // otherwise if the fetch is patched by user code, we will be patching it\n          // too late and there won't be any caching behaviors\n          ComponentMod.patchFetch()\n\n          const webpackRequire = ComponentMod.__next_app__.require\n          if (webpackRequire?.m) {\n            for (const id of Object.keys(webpackRequire.m)) {\n              await webpackRequire(id)\n            }\n          }\n        })\n        .catch(() => {})\n    }\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets, it's only used for HMR in\n    // development.\n  }\n\n  protected async loadInstrumentationModule() {\n    if (!this.serverOptions.dev) {\n      try {\n        this.instrumentation = await getInstrumentationModule(\n          this.dir,\n          this.nextConfig.distDir\n        )\n      } catch (err: any) {\n        if (err.code !== 'MODULE_NOT_FOUND') {\n          throw new Error(\n            'An error occurred while loading the instrumentation hook',\n            { cause: err }\n          )\n        }\n      }\n    }\n    return this.instrumentation\n  }\n\n  protected async prepareImpl() {\n    await super.prepareImpl()\n    await this.runInstrumentationHookIfAvailable()\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await ensureInstrumentationRegistered(this.dir, this.nextConfig.distDir)\n  }\n\n  protected loadEnvConfig({\n    dev,\n    forceReload,\n    silent,\n  }: {\n    dev: boolean\n    forceReload?: boolean\n    silent?: boolean\n  }) {\n    loadEnvConfig(\n      this.dir,\n      dev,\n      silent ? { info: () => {}, error: () => {} } : Log,\n      forceReload\n    )\n  }\n\n  private async loadCustomCacheHandlers() {\n    const { cacheHandlers } = this.nextConfig.experimental\n    if (!cacheHandlers) return\n\n    // If we've already initialized the cache handlers interface, don't do it\n    // again.\n    if (!initializeCacheHandlers()) return\n\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, handler)\n          )\n        )\n      )\n    }\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n  }) {\n    const dev = !!this.renderOpts.dev\n    let CacheHandler: any\n    const { cacheHandler } = this.nextConfig\n\n    if (cacheHandler) {\n      CacheHandler = interopDefault(\n        await dynamicImportEsmDefault(\n          formatDynamicImportPath(this.distDir, cacheHandler)\n        )\n      )\n    }\n\n    await this.loadCustomCacheHandlers()\n\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      fs: this.getCacheFilesystem(),\n      dev,\n      requestHeaders,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      serverDistDir: this.serverDistDir,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk:\n        !this.minimalMode && this.nextConfig.experimental.isrFlushToDisk,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n      CurCacheHandler: CacheHandler,\n    })\n  }\n\n  protected getResponseCache() {\n    return new ResponseCache(this.minimalMode)\n  }\n\n  protected getPublicDir(): string {\n    return join(this.dir, CLIENT_PUBLIC_FILES_PATH)\n  }\n\n  protected getHasStaticDir(): boolean {\n    return fs.existsSync(join(this.dir, 'static'))\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return loadManifest(\n      join(this.serverDistDir, PAGES_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return loadManifest(\n      join(this.serverDistDir, APP_PATHS_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    if (!this.enabledDirectories.app) return []\n\n    const routesManifest = this.getRoutesManifest()\n    return (\n      routesManifest?.rewrites.beforeFiles\n        .filter(isInterceptionRouteRewrite)\n        .map((rewrite) => new RegExp(rewrite.regex)) ?? []\n    )\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    return !!getMaybePagePath(\n      pathname,\n      this.distDir,\n      this.nextConfig.i18n?.locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected getBuildId(): string {\n    const buildIdFile = join(this.distDir, BUILD_ID_FILE)\n    try {\n      return fs.readFileSync(buildIdFile, 'utf8').trim()\n    } catch (err: any) {\n      if (err.code === 'ENOENT') {\n        throw new Error(\n          `Could not find a production build in the '${this.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n        )\n      }\n\n      throw err\n    }\n  }\n\n  protected getEnabledDirectories(dev: boolean): NextEnabledDirectories {\n    const dir = dev ? this.dir : this.serverDistDir\n\n    return {\n      app: findDir(dir, 'app') ? true : false,\n      pages: findDir(dir, 'pages') ? true : false,\n    }\n  }\n\n  protected sendRenderResult(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    return sendRenderResult({\n      req: req.originalRequest,\n      res: res.originalResponse,\n      result: options.result,\n      type: options.type,\n      generateEtags: options.generateEtags,\n      poweredByHeader: options.poweredByHeader,\n      cacheControl: options.cacheControl,\n    })\n  }\n\n  protected async runApi(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages()\n\n    for (const edgeFunctionsPage of edgeFunctionsPages) {\n      if (edgeFunctionsPage === match.definition.pathname) {\n        const handledAsEdgeFunction = await this.runEdgeFunction({\n          req,\n          res,\n          query,\n          params: match.params,\n          page: match.definition.pathname,\n          appPaths: null,\n        })\n\n        if (handledAsEdgeFunction) {\n          return true\n        }\n      }\n    }\n    // The module supports minimal mode, load the minimal module.\n    // Restore original URL as the handler handles it's own parsing\n    const parsedInitUrl = parseUrl(getRequestMeta(req, 'initURL') || req.url)\n    req.url = `${parsedInitUrl.pathname}${parsedInitUrl.search || ''}`\n\n    const loader = new NodeModuleLoader()\n    const module = (await loader.load(match.definition.filename)) as {\n      handler: (\n        req: IncomingMessage,\n        res: ServerResponse,\n        ctx: {\n          waitUntil: ReturnType<BaseServer['getWaitUntil']>\n        }\n      ) => Promise<void>\n    }\n    addRequestMeta(req.originalRequest, 'projectDir', this.dir)\n    addRequestMeta(req.originalRequest, 'distDir', this.distDir)\n    await module.handler(req.originalRequest, res.originalResponse, {\n      waitUntil: this.getWaitUntil(),\n    })\n    return true\n  }\n\n  protected async renderHTML(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    return getTracer().trace(NextNodeServerSpan.renderHTML, async () =>\n      this.renderHTMLImpl(req, res, pathname, query, renderOpts)\n    )\n  }\n\n  private async renderHTMLImpl(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Invariant: renderHTML should not be called in minimal mode'\n      )\n      // the `else` branch is needed for tree-shaking\n    } else {\n      // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n      // object here but only updating its `nextFontManifest` field.\n      // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n      renderOpts.nextFontManifest = this.nextFontManifest\n\n      if (this.enabledDirectories.app && renderOpts.isAppPath) {\n        return lazyRenderAppPage(\n          req,\n          res,\n          pathname,\n          query,\n          // This code path does not service revalidations for unknown param\n          // shells. As a result, we don't need to pass in the unknown params.\n          null,\n          renderOpts,\n          this.getServerComponentsHmrCache(),\n          false,\n          {\n            buildId: this.buildId,\n          }\n        )\n      }\n\n      // TODO: re-enable this once we've refactored to use implicit matches\n      // throw new Error('Invariant: render should have used routeModule')\n\n      return lazyRenderPagesPage(\n        req.originalRequest,\n        res.originalResponse,\n        pathname,\n        query,\n        renderOpts,\n        {\n          buildId: this.buildId,\n          deploymentId: this.nextConfig.deploymentId,\n          customServer: this.serverOptions.customServer || undefined,\n        },\n        {\n          isFallback: false,\n          isDraftMode: renderOpts.isDraftMode,\n          developmentNotFoundSourcePage: getRequestMeta(\n            req,\n            'developmentNotFoundSourcePage'\n          ),\n        }\n      )\n    }\n  }\n\n  protected async imageOptimizer(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    paramsResult: import('./image-optimizer').ImageParamsResult,\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  ): Promise<{\n    buffer: Buffer\n    contentType: string\n    maxAge: number\n    upstreamEtag: string\n    etag: string\n  }> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: imageOptimizer should not be called in minimal mode'\n      )\n    } else {\n      const { imageOptimizer, fetchExternalImage, fetchInternalImage } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const handleInternalReq = async (\n        newReq: IncomingMessage,\n        newRes: ServerResponse\n      ) => {\n        if (newReq.url === req.url) {\n          throw new Error(`Invariant attempted to optimize _next/image itself`)\n        }\n\n        if (!this.routerServerHandler) {\n          throw new Error(`Invariant missing routerServerHandler`)\n        }\n\n        await this.routerServerHandler(newReq, newRes)\n        return\n      }\n\n      const { isAbsolute, href } = paramsResult\n\n      const imageUpstream = isAbsolute\n        ? await fetchExternalImage(href)\n        : await fetchInternalImage(\n            href,\n            req.originalRequest,\n            res.originalResponse,\n            handleInternalReq\n          )\n\n      return imageOptimizer(imageUpstream, paramsResult, this.nextConfig, {\n        isDev: this.renderOpts.dev,\n        previousCacheEntry,\n      })\n    }\n  }\n\n  protected getPagePath(pathname: string, locales?: string[]): string {\n    return getPagePath(\n      pathname,\n      this.distDir,\n      locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages() || []\n    if (edgeFunctionsPages.length) {\n      const appPaths = this.getOriginalAppPaths(ctx.pathname)\n      const isAppPath = Array.isArray(appPaths)\n\n      let page = ctx.pathname\n      if (isAppPath) {\n        // When it's an array, we need to pass all parallel routes to the loader.\n        page = appPaths[0]\n      }\n\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        if (edgeFunctionsPage === page) {\n          await this.runEdgeFunction({\n            req: ctx.req,\n            res: ctx.res,\n            query: ctx.query,\n            params: ctx.renderOpts.params,\n            page,\n            appPaths,\n          })\n          return null\n        }\n      }\n    }\n\n    return super.renderPageComponent(ctx, bubbleNoFallback)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    return getTracer().trace(\n      NextNodeServerSpan.findPageComponents,\n      {\n        spanName: 'resolve page components',\n        attributes: {\n          'next.route': isAppPath ? normalizeAppPath(page) : page,\n        },\n      },\n      () =>\n        this.findPageComponentsImpl({\n          locale,\n          page,\n          query,\n          params,\n          isAppPath,\n          url,\n        })\n    )\n  }\n\n  private async findPageComponentsImpl({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url: _url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    const pagePaths: string[] = [page]\n    if (query.amp) {\n      // try serving a static AMP version first\n      pagePaths.unshift(\n        (isAppPath ? normalizeAppPath(page) : normalizePagePath(page)) + '.amp'\n      )\n    }\n\n    if (locale) {\n      pagePaths.unshift(\n        ...pagePaths.map((path) => `/${locale}${path === '/' ? '' : path}`)\n      )\n    }\n\n    for (const pagePath of pagePaths) {\n      try {\n        const components = await loadComponents({\n          distDir: this.distDir,\n          page: pagePath,\n          isAppPath,\n          isDev: this.isDev,\n          sriEnabled: this.sriEnabled,\n        })\n\n        if (\n          locale &&\n          typeof components.Component === 'string' &&\n          !pagePath.startsWith(`/${locale}/`) &&\n          pagePath !== `/${locale}`\n        ) {\n          // if loading an static HTML file the locale is required\n          // to be present since all HTML files are output under their locale\n          continue\n        }\n\n        return {\n          components,\n          query: {\n            ...(!this.renderOpts.isExperimentalCompile &&\n            components.getStaticProps\n              ? ({\n                  amp: query.amp,\n                } as NextParsedUrlQuery)\n              : query),\n            // For appDir params is excluded.\n            ...((isAppPath ? {} : params) || {}),\n          },\n        }\n      } catch (err) {\n        // we should only not throw if we failed to find the page\n        // in the pages-manifest\n        if (!(err instanceof PageNotFoundError)) {\n          throw err\n        }\n      }\n    }\n    return null\n  }\n\n  protected getNextFontManifest(): NextFontManifest | undefined {\n    return loadManifest(\n      join(this.distDir, 'server', NEXT_FONT_MANIFEST + '.json')\n    ) as NextFontManifest\n  }\n\n  protected handleNextImageRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname || !parsedUrl.pathname.startsWith('/_next/image')) {\n      return false\n    }\n    // Ignore if its a middleware request\n    if (getRequestMeta(req, 'middlewareInvoke')) {\n      return false\n    }\n\n    if (\n      this.minimalMode ||\n      this.nextConfig.output === 'export' ||\n      process.env.NEXT_MINIMAL\n    ) {\n      res.statusCode = 400\n      res.body('Bad Request').send()\n      return true\n      // the `else` branch is needed for tree-shaking\n    } else {\n      const { ImageOptimizerCache } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const imageOptimizerCache = new ImageOptimizerCache({\n        distDir: this.distDir,\n        nextConfig: this.nextConfig,\n      })\n\n      const { sendResponse, ImageError } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      if (!this.imageResponseCache) {\n        throw new Error('invariant image optimizer cache was not initialized')\n      }\n      const imagesConfig = this.nextConfig.images\n\n      if (imagesConfig.loader !== 'default' || imagesConfig.unoptimized) {\n        await this.render404(req, res)\n        return true\n      }\n\n      const paramsResult = ImageOptimizerCache.validateParams(\n        req.originalRequest,\n        parsedUrl.query,\n        this.nextConfig,\n        !!this.renderOpts.dev\n      )\n\n      if ('errorMessage' in paramsResult) {\n        res.statusCode = 400\n        res.body(paramsResult.errorMessage).send()\n        return true\n      }\n\n      const cacheKey = ImageOptimizerCache.getCacheKey(paramsResult)\n\n      try {\n        const { getExtension } =\n          require('./serve-static') as typeof import('./serve-static')\n        const cacheEntry = await this.imageResponseCache.get(\n          cacheKey,\n          async ({ previousCacheEntry }) => {\n            const { buffer, contentType, maxAge, upstreamEtag, etag } =\n              await this.imageOptimizer(\n                req,\n                res,\n                paramsResult,\n                previousCacheEntry\n              )\n\n            return {\n              value: {\n                kind: CachedRouteKind.IMAGE,\n                buffer,\n                etag,\n                extension: getExtension(contentType) as string,\n                upstreamEtag,\n              },\n              cacheControl: { revalidate: maxAge, expire: undefined },\n            }\n          },\n          {\n            routeKind: RouteKind.IMAGE,\n            incrementalCache: imageOptimizerCache,\n            isFallback: false,\n          }\n        )\n\n        if (cacheEntry?.value?.kind !== CachedRouteKind.IMAGE) {\n          throw new Error(\n            'invariant did not get entry from image response cache'\n          )\n        }\n\n        sendResponse(\n          req.originalRequest,\n          res.originalResponse,\n          paramsResult.href,\n          cacheEntry.value.extension,\n          cacheEntry.value.buffer,\n          cacheEntry.value.etag,\n          paramsResult.isStatic,\n          cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT',\n          imagesConfig,\n          cacheEntry.cacheControl?.revalidate || 0,\n          Boolean(this.renderOpts.dev)\n        )\n        return true\n      } catch (err) {\n        if (err instanceof ImageError) {\n          res.statusCode = err.statusCode\n          res.body(err.message).send()\n          return true\n        }\n        throw err\n      }\n    }\n  }\n\n  protected handleCatchallRenderRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('Invariant: pathname is undefined')\n    }\n\n    // When in minimal mode we do not bubble the fallback as the\n    // router-server is not present to handle the error\n    addRequestMeta(req, 'bubbleNoFallback', this.minimalMode ? undefined : true)\n\n    // TODO: this is only needed until route-module can handle\n    // rendering/serving the 404 directly with next-server\n    if (!routerServerGlobal[RouterServerContextSymbol]) {\n      routerServerGlobal[RouterServerContextSymbol] = {}\n    }\n    const relativeProjectDir = relative(process.cwd(), this.dir)\n    const existingServerContext =\n      routerServerGlobal[RouterServerContextSymbol][relativeProjectDir]\n\n    if (!existingServerContext) {\n      routerServerGlobal[RouterServerContextSymbol][relativeProjectDir] = {\n        render404: this.render404.bind(this),\n      }\n    }\n    routerServerGlobal[RouterServerContextSymbol][\n      relativeProjectDir\n    ].nextConfig = this.nextConfig\n\n    try {\n      // next.js core assumes page path without trailing slash\n      pathname = removeTrailingSlash(pathname)\n\n      const options: MatchOptions = {\n        i18n: this.i18nProvider?.fromRequest(req, pathname),\n      }\n      const match = await this.matchers.match(pathname, options)\n\n      // If we don't have a match, try to render it anyways.\n      if (!match) {\n        await this.render(req, res, pathname, query, parsedUrl, true)\n\n        return true\n      }\n\n      // Add the match to the request so we don't have to re-run the matcher\n      // for the same request.\n      addRequestMeta(req, 'match', match)\n\n      // TODO-APP: move this to a route handler\n      const edgeFunctionsPages = this.getEdgeFunctionsPages()\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        // If the page doesn't match the edge function page, skip it.\n        if (edgeFunctionsPage !== match.definition.page) continue\n\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n        delete query[NEXT_RSC_UNION_QUERY]\n\n        // If we handled the request, we can return early.\n        // For api routes edge runtime\n        try {\n          const handled = await this.runEdgeFunction({\n            req,\n            res,\n            query,\n            params: match.params,\n            page: match.definition.page,\n            match,\n            appPaths: null,\n          })\n          if (handled) return true\n        } catch (apiError) {\n          await this.instrumentationOnRequestError(apiError, req, {\n            routePath: match.definition.page,\n            routerKind: 'Pages Router',\n            routeType: 'route',\n            // Edge runtime does not support ISR\n            revalidateReason: undefined,\n          })\n          throw apiError\n        }\n      }\n\n      // If the route was detected as being a Pages API route, then handle\n      // it.\n      // TODO: move this behavior into a route handler.\n      if (isPagesAPIRouteMatch(match)) {\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n\n        const handled = await this.handleApiRequest(req, res, query, match)\n        if (handled) return true\n      }\n\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      try {\n        if (this.renderOpts.dev) {\n          const { formatServerError } =\n            require('../lib/format-server-error') as typeof import('../lib/format-server-error')\n          formatServerError(err)\n          this.logErrorWithOriginalStack(err)\n        } else {\n          this.logError(err)\n        }\n        res.statusCode = 500\n        await this.renderError(err, req, res, pathname, query)\n        return true\n      } catch {}\n\n      throw err\n    }\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected logErrorWithOriginalStack(\n    _err?: unknown,\n    _type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    throw new Error(\n      'Invariant: logErrorWithOriginalStack can only be called on the development server'\n    )\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected async ensurePage(_opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    match?: RouteMatch\n    url?: string\n  }): Promise<void> {\n    throw new Error(\n      'Invariant: ensurePage can only be called on the development server'\n    )\n  }\n\n  /**\n   * Resolves `API` request, in development builds on demand\n   * @param req http request\n   * @param res http response\n   * @param pathname path of request\n   */\n  protected async handleApiRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    return this.runApi(req, res, query, match)\n  }\n\n  protected getCacheFilesystem(): CacheFs {\n    return nodeFs\n  }\n\n  protected normalizeReq(\n    req: NodeNextRequest | IncomingMessage\n  ): NodeNextRequest {\n    return !(req instanceof NodeNextRequest) ? new NodeNextRequest(req) : req\n  }\n\n  protected normalizeRes(\n    res: NodeNextResponse | ServerResponse\n  ): NodeNextResponse {\n    return !(res instanceof NodeNextResponse) ? new NodeNextResponse(res) : res\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = this.makeRequestHandler()\n    if (this.serverOptions.experimentalTestProxy) {\n      const { wrapRequestHandlerNode } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server') as typeof import('../experimental/testmode/server')\n      return wrapRequestHandlerNode(handler)\n    }\n    return handler\n  }\n\n  private makeRequestHandler(): NodeRequestHandler {\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause an unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    this.prepare().catch((err) => {\n      console.error('Failed to prepare server', err)\n    })\n\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) =>\n      handler(this.normalizeReq(req), this.normalizeRes(res), parsedUrl)\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts,\n  }: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    const handler = this.getRequestHandler()\n    await handler(\n      new NodeNextRequest(mocked.req),\n      new NodeNextResponse(mocked.res)\n    )\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      mocked.res.statusCode !== 200 &&\n      !(mocked.res.statusCode === 404 && opts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n  }\n\n  public async render(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    parsedUrl?: NextUrlWithParsedQuery,\n    internal = false\n  ): Promise<void> {\n    return super.render(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      parsedUrl,\n      internal\n    )\n  }\n\n  public async renderToHTML(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderToHTML(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    err: Error | null\n  ) {\n    const { req, res, query } = ctx\n    const is404 = res.statusCode === 404\n\n    if (is404 && this.enabledDirectories.app) {\n      if (this.renderOpts.dev) {\n        await this.ensurePage({\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          clientOnly: false,\n          url: req.url,\n        }).catch(() => {})\n      }\n\n      if (\n        this.getEdgeFunctionsPages().includes(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      ) {\n        await this.runEdgeFunction({\n          req,\n          res,\n          query: query || {},\n          params: {},\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          appPaths: null,\n        })\n        return null\n      }\n    }\n    return super.renderErrorToResponseImpl(ctx, err)\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.renderError(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      setHeaders\n    )\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderErrorToHTML(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  public async render404(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.render404(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      parsedUrl,\n      setHeaders\n    )\n  }\n\n  protected getMiddlewareManifest(): MiddlewareManifest | null {\n    if (this.minimalMode) {\n      return null\n    } else {\n      const manifest: MiddlewareManifest = require(this.middlewareManifestPath)\n      return manifest\n    }\n  }\n\n  /** Returns the middleware routing item if there is one. */\n  protected async getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    const manifest = this.getMiddlewareManifest()\n    const middleware = manifest?.middleware?.['/']\n    if (!middleware) {\n      const middlewareModule = await this.loadNodeMiddleware()\n\n      if (middlewareModule) {\n        return {\n          match: getMiddlewareRouteMatcher(\n            middlewareModule.config?.matchers || [\n              { regexp: '.*', originalSource: '/:path*' },\n            ]\n          ),\n          page: '/',\n        }\n      }\n\n      return\n    }\n\n    return {\n      match: getMiddlewareMatcher(middleware),\n      page: '/',\n    }\n  }\n\n  protected getEdgeFunctionsPages(): string[] {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return []\n    }\n\n    return Object.keys(manifest.functions)\n  }\n\n  /**\n   * Get information for the edge function located in the provided page\n   * folder. If the edge function info can't be found it will throw\n   * an error.\n   */\n  protected getEdgeFunctionInfo(params: {\n    page: string\n    /** Whether we should look for a middleware or not */\n    middleware: boolean\n  }): {\n    name: string\n    paths: string[]\n    wasm: { filePath: string; name: string }[]\n    env: { [key: string]: string }\n    assets?: { filePath: string; name: string }[]\n  } | null {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return null\n    }\n\n    let foundPage: string\n\n    try {\n      foundPage = denormalizePagePath(normalizePagePath(params.page))\n    } catch (err) {\n      return null\n    }\n\n    let pageInfo = params.middleware\n      ? manifest.middleware[foundPage]\n      : manifest.functions[foundPage]\n\n    if (!pageInfo) {\n      if (!params.middleware) {\n        throw new PageNotFoundError(foundPage)\n      }\n      return null\n    }\n\n    return {\n      name: pageInfo.name,\n      paths: pageInfo.files.map((file) => join(this.distDir, file)),\n      wasm: (pageInfo.wasm ?? []).map((binding) => ({\n        ...binding,\n        filePath: join(this.distDir, binding.filePath),\n      })),\n      assets:\n        pageInfo.assets &&\n        pageInfo.assets.map((binding) => {\n          return {\n            ...binding,\n            filePath: join(this.distDir, binding.filePath),\n          }\n        }),\n      env: pageInfo.env,\n    }\n  }\n\n  private async loadNodeMiddleware() {\n    if (!process.env.NEXT_MINIMAL) {\n      if (!this.nextConfig.experimental.nodeMiddleware) {\n        return\n      }\n\n      try {\n        const functionsConfig = this.renderOpts.dev\n          ? {}\n          : require(join(this.distDir, 'server', FUNCTIONS_CONFIG_MANIFEST))\n\n        if (\n          this.renderOpts.dev ||\n          functionsConfig?.functions?.['/_middleware']\n        ) {\n          // if used with top level await, this will be a promise\n          return require(join(this.distDir, 'server', 'middleware.js'))\n        }\n      } catch (err) {\n        if (\n          isError(err) &&\n          err.code !== 'ENOENT' &&\n          err.code !== 'MODULE_NOT_FOUND'\n        ) {\n          throw err\n        }\n      }\n    }\n  }\n\n  /**\n   * Checks if a middleware exists. This method is useful for the development\n   * server where we need to check the filesystem. Here we just check the\n   * middleware manifest.\n   */\n  protected async hasMiddleware(pathname: string): Promise<boolean> {\n    const info = this.getEdgeFunctionInfo({ page: pathname, middleware: true })\n    const nodeMiddleware = await this.loadNodeMiddleware()\n\n    if (!info && nodeMiddleware) {\n      return true\n    }\n    return Boolean(info && info.paths.length > 0)\n  }\n\n  /**\n   * A placeholder for a function to be defined in the development server.\n   * It will make sure that the root middleware or an edge function has been compiled\n   * so that we can run it.\n   */\n  protected async ensureMiddleware(_url?: string) {}\n  protected async ensureEdgeFunction(_params: {\n    page: string\n    appPaths: string[] | null\n    url?: string\n  }) {}\n\n  /**\n   * This method gets all middleware matchers and execute them when the request\n   * matches. It will make sure that each middleware exists and is compiled and\n   * ready to be invoked. The development server will decorate it to add warns\n   * and errors with rich traces.\n   */\n  protected async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    onWarning?: (warning: Error) => void\n  }) {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: runMiddleware should not be called in minimal mode'\n      )\n    }\n\n    // Middleware is skipped for on-demand revalidate requests\n    if (\n      checkIsOnDemandRevalidate(params.request, this.renderOpts.previewProps)\n        .isOnDemandRevalidate\n    ) {\n      return {\n        response: new Response(null, { headers: { 'x-middleware-next': '1' } }),\n      } as FetchEventResult\n    }\n\n    let url: string\n\n    if (this.nextConfig.skipMiddlewareUrlNormalize) {\n      url = getRequestMeta(params.request, 'initURL')!\n    } else {\n      // For middleware to \"fetch\" we must always provide an absolute URL\n      const query = urlQueryToSearchParams(params.parsed.query).toString()\n      const locale = getRequestMeta(params.request, 'locale')\n\n      url = `${getRequestMeta(params.request, 'initProtocol')}://${\n        this.fetchHostname || 'localhost'\n      }:${this.port}${locale ? `/${locale}` : ''}${params.parsed.pathname}${\n        query ? `?${query}` : ''\n      }`\n    }\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const page: {\n      name?: string\n      params?: { [key: string]: string | string[] }\n    } = {}\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return { finished: false }\n    }\n    if (!(await this.hasMiddleware(middleware.page))) {\n      return { finished: false }\n    }\n\n    await this.ensureMiddleware(params.request.url)\n    const middlewareInfo = this.getEdgeFunctionInfo({\n      page: middleware.page,\n      middleware: true,\n    })\n\n    const method = (params.request.method || 'GET').toUpperCase()\n    const requestData = {\n      headers: params.request.headers,\n      method,\n      nextConfig: {\n        basePath: this.nextConfig.basePath,\n        i18n: this.nextConfig.i18n,\n        trailingSlash: this.nextConfig.trailingSlash,\n        experimental: this.nextConfig.experimental,\n      },\n      url: url,\n      page,\n      body:\n        method !== 'GET' && method !== 'HEAD'\n          ? (getRequestMeta(params.request, 'clonableBody') as any)\n          : undefined,\n\n      signal: signalFromNodeResponse(params.response.originalResponse),\n      waitUntil: this.getWaitUntil(),\n    }\n    let result:\n      | UnwrapPromise<ReturnType<typeof import('./web/sandbox').run>>\n      | undefined\n\n    // if no middleware info check for Node.js middleware\n    // this is not in the middleware-manifest as that historically\n    // has only included edge-functions, we need to do a breaking\n    // version bump for that manifest to write this info there if\n    // we decide we want to\n    if (!middlewareInfo) {\n      let middlewareModule\n      middlewareModule = await this.loadNodeMiddleware()\n\n      if (!middlewareModule) {\n        throw new MiddlewareNotFoundError()\n      }\n      const adapterFn: typeof import('./web/adapter').adapter =\n        middlewareModule.default || middlewareModule\n\n      const hasRequestBody =\n        !['HEAD', 'GET'].includes(params.request.method) &&\n        Boolean(requestData.body)\n\n      try {\n        result = await adapterFn({\n          handler: middlewareModule.middleware || middlewareModule,\n          request: {\n            ...requestData,\n            body: hasRequestBody\n              ? requestData.body.cloneBodyStream()\n              : undefined,\n          },\n          page: 'middleware',\n        })\n      } finally {\n        if (hasRequestBody) {\n          requestData.body.finalize()\n        }\n      }\n    } else {\n      const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n\n      result = await run({\n        distDir: this.distDir,\n        name: middlewareInfo.name,\n        paths: middlewareInfo.paths,\n        edgeFunctionEntry: middlewareInfo,\n        request: requestData,\n        useCache: true,\n        onWarning: params.onWarning,\n      })\n    }\n\n    if (!this.renderOpts.dev) {\n      result.waitUntil.catch((error) => {\n        console.error(`Uncaught: middleware waitUntil errored`, error)\n      })\n    }\n\n    if (!result) {\n      this.render404(params.request, params.response, params.parsed)\n      return { finished: true }\n    }\n\n    // Split compound (comma-separated) set-cookie headers\n    if (result.response.headers.has('set-cookie')) {\n      const cookies = result.response.headers\n        .getSetCookie()\n        .flatMap((maybeCompoundCookie) =>\n          splitCookiesString(maybeCompoundCookie)\n        )\n\n      // Clear existing header(s)\n      result.response.headers.delete('set-cookie')\n\n      // Append each cookie individually.\n      for (const cookie of cookies) {\n        result.response.headers.append('set-cookie', cookie)\n      }\n\n      // Add cookies to request meta.\n      addRequestMeta(params.request, 'middlewareCookie', cookies)\n    }\n\n    return result\n  }\n\n  protected handleCatchallMiddlewareRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsed\n  ) => {\n    const isMiddlewareInvoke = getRequestMeta(req, 'middlewareInvoke')\n\n    if (!isMiddlewareInvoke) {\n      return false\n    }\n\n    const handleFinished = () => {\n      addRequestMeta(req, 'middlewareInvoke', true)\n      res.body('').send()\n      return true\n    }\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return handleFinished()\n    }\n\n    const initUrl = getRequestMeta(req, 'initURL')!\n    const parsedUrl = parseUrl(initUrl)\n    const pathnameInfo = getNextPathnameInfo(parsedUrl.pathname, {\n      nextConfig: this.nextConfig,\n      i18nProvider: this.i18nProvider,\n    })\n\n    parsedUrl.pathname = pathnameInfo.pathname\n    const normalizedPathname = removeTrailingSlash(parsed.pathname || '')\n    let maybeDecodedPathname = normalizedPathname\n\n    try {\n      maybeDecodedPathname = decodeURIComponent(normalizedPathname)\n    } catch {\n      /* non-fatal we can't decode so can't match it */\n    }\n\n    if (\n      !(\n        middleware.match(normalizedPathname, req, parsedUrl.query) ||\n        middleware.match(maybeDecodedPathname, req, parsedUrl.query)\n      )\n    ) {\n      return handleFinished()\n    }\n\n    let result: Awaited<\n      ReturnType<typeof NextNodeServer.prototype.runMiddleware>\n    >\n    let bubblingResult = false\n\n    try {\n      await this.ensureMiddleware(req.url)\n\n      result = await this.runMiddleware({\n        request: req,\n        response: res,\n        parsedUrl: parsedUrl,\n        parsed: parsed,\n      })\n\n      if ('response' in result) {\n        if (isMiddlewareInvoke) {\n          bubblingResult = true\n          throw new BubbledError(true, result)\n        }\n\n        for (const [key, value] of Object.entries(\n          toNodeOutgoingHttpHeaders(result.response.headers)\n        )) {\n          if (key !== 'content-encoding' && value !== undefined) {\n            res.setHeader(key, value as string | string[])\n          }\n        }\n        res.statusCode = result.response.status\n\n        const { originalResponse } = res\n        if (result.response.body) {\n          await pipeToNodeResponse(result.response.body, originalResponse)\n        } else {\n          originalResponse.end()\n        }\n        return true\n      }\n    } catch (err: unknown) {\n      if (bubblingResult) {\n        throw err\n      }\n\n      if (isError(err) && err.code === 'ENOENT') {\n        await this.render404(req, res, parsed)\n        return true\n      }\n\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        await this.renderError(err, req, res, parsed.pathname || '')\n        return true\n      }\n\n      const error = getProperError(err)\n      console.error(error)\n      res.statusCode = 500\n      await this.renderError(error, req, res, parsed.pathname || '')\n      return true\n    }\n\n    return result.finished\n  }\n\n  private _cachedPreviewManifest: PrerenderManifest | undefined\n  protected getPrerenderManifest(): PrerenderManifest {\n    if (this._cachedPreviewManifest) {\n      return this._cachedPreviewManifest\n    }\n\n    this._cachedPreviewManifest = loadManifest(\n      join(this.distDir, PRERENDER_MANIFEST)\n    ) as PrerenderManifest\n\n    return this._cachedPreviewManifest\n  }\n\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    return getTracer().trace(\n      NextNodeServerSpan.getRoutesManifest,\n      () => loadManifest(join(this.distDir, ROUTES_MANIFEST)) as RoutesManifest\n    )\n  }\n\n  protected attachRequestMeta(\n    req: NodeNextRequest,\n    parsedUrl: NextUrlWithParsedQuery,\n    isUpgradeReq?: boolean\n  ) {\n    // Injected in base-server.ts\n    const protocol = req.headers['x-forwarded-proto']?.includes('https')\n      ? 'https'\n      : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl =\n      this.fetchHostname && this.port\n        ? `${protocol}://${this.fetchHostname}:${this.port}${req.url}`\n        : this.nextConfig.experimental.trustHostHeader\n          ? `https://${req.headers.host || 'localhost'}${req.url}`\n          : req.url\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req.originalRequest))\n    }\n  }\n\n  protected async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    match?: RouteMatch\n    onError?: (err: unknown) => void\n    onWarning?: (warning: Error) => void\n  }): Promise<FetchEventResult | null> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Middleware is not supported in minimal mode. Please remove the `NEXT_MINIMAL` environment variable.'\n      )\n    }\n    let edgeInfo: ReturnType<typeof this.getEdgeFunctionInfo> | undefined\n\n    const { query, page, match } = params\n\n    if (!match)\n      await this.ensureEdgeFunction({\n        page,\n        appPaths: params.appPaths,\n        url: params.req.url,\n      })\n    edgeInfo = this.getEdgeFunctionInfo({\n      page,\n      middleware: false,\n    })\n\n    if (!edgeInfo) {\n      return null\n    }\n\n    // For edge to \"fetch\" we must always provide an absolute URL\n    const isNextDataRequest = getRequestMeta(params.req, 'isNextDataReq')\n    const initialUrl = new URL(\n      getRequestMeta(params.req, 'initURL') || '/',\n      'http://n'\n    )\n    const queryString = urlQueryToSearchParams({\n      ...Object.fromEntries(initialUrl.searchParams),\n      ...query,\n      ...params.params,\n    }).toString()\n\n    if (isNextDataRequest) {\n      params.req.headers['x-nextjs-data'] = '1'\n    }\n    initialUrl.search = queryString\n    const url = initialUrl.toString()\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n    const result = await run({\n      distDir: this.distDir,\n      name: edgeInfo.name,\n      paths: edgeInfo.paths,\n      edgeFunctionEntry: edgeInfo,\n      request: {\n        headers: params.req.headers,\n        method: params.req.method,\n        nextConfig: {\n          basePath: this.nextConfig.basePath,\n          i18n: this.nextConfig.i18n,\n          trailingSlash: this.nextConfig.trailingSlash,\n        },\n        url,\n        page: {\n          name: params.page,\n          ...(params.params && { params: params.params }),\n        },\n        body: getRequestMeta(params.req, 'clonableBody'),\n        signal: signalFromNodeResponse(params.res.originalResponse),\n        waitUntil: this.getWaitUntil(),\n      },\n      useCache: true,\n      onError: params.onError,\n      onWarning: params.onWarning,\n      incrementalCache:\n        (globalThis as any).__incrementalCache ||\n        getRequestMeta(params.req, 'incrementalCache'),\n      serverComponentsHmrCache: getRequestMeta(\n        params.req,\n        'serverComponentsHmrCache'\n      ),\n    })\n\n    if (result.fetchMetrics) {\n      params.req.fetchMetrics = result.fetchMetrics\n    }\n\n    if (!params.res.statusCode || params.res.statusCode < 400) {\n      params.res.statusCode = result.response.status\n      params.res.statusMessage = result.response.statusText\n    }\n\n    // TODO: (wyattjoh) investigate improving this\n\n    result.response.headers.forEach((value, key) => {\n      // The append handling is special cased for `set-cookie`.\n      if (key.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          params.res.appendHeader(key, cookie)\n        }\n      } else {\n        params.res.appendHeader(key, value)\n      }\n    })\n\n    const { originalResponse } = params.res\n    if (result.response.body) {\n      await pipeToNodeResponse(result.response.body, originalResponse)\n    } else {\n      originalResponse.end()\n    }\n\n    return result\n  }\n\n  protected get serverDistDir(): string {\n    if (this._serverDistDir) {\n      return this._serverDistDir\n    }\n    const serverDistDir = join(this.distDir, SERVER_DIRECTORY)\n    this._serverDistDir = serverDistDir\n    return serverDistDir\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // Not implemented for production use cases, this is implemented on the\n    // development server.\n    return null\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    // For Node.js runtime production logs, in dev it will be overridden by next-dev-server\n    if (!this.renderOpts.dev) {\n      this.logError(args[0] as Error)\n    }\n  }\n\n  protected onServerClose(listener: () => Promise<void>) {\n    this.cleanupListeners.add(listener)\n  }\n\n  async close(): Promise<void> {\n    await this.cleanupListeners.runAll()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil {\n    this.internalWaitUntil ??= this.createInternalWaitUntil()\n    return this.internalWaitUntil\n  }\n\n  private createInternalWaitUntil() {\n    if (this.minimalMode) {\n      throw new InvariantError(\n        'createInternalWaitUntil should never be called in minimal mode'\n      )\n    }\n\n    const awaiter = new AwaiterOnce({ onError: console.error })\n\n    // TODO(after): warn if the process exits before these are awaited\n    this.onServerClose(() => awaiter.awaiting())\n\n    return awaiter.waitUntil\n  }\n}\n"], "names": ["DecodeError", "PageNotFoundError", "MiddlewareNotFoundError", "fs", "join", "relative", "getRouteMatcher", "addRequestMeta", "getRequestMeta", "PAGES_MANIFEST", "BUILD_ID_FILE", "MIDDLEWARE_MANIFEST", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "APP_PATHS_MANIFEST", "SERVER_DIRECTORY", "NEXT_FONT_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "FUNCTIONS_CONFIG_MANIFEST", "findDir", "NodeNextRequest", "NodeNextResponse", "sendRenderResult", "parseUrl", "Log", "BaseServer", "getMaybePagePath", "getPagePath", "denormalizePagePath", "normalizePagePath", "loadComponents", "isError", "getProperError", "splitCookiesString", "toNodeOutgoingHttpHeaders", "getMiddlewareRouteMatcher", "loadEnvConfig", "urlQueryToSearchParams", "removeTrailingSlash", "getNextPathnameInfo", "getCloneableBody", "checkIsOnDemandRevalidate", "ResponseCache", "CachedRouteKind", "IncrementalCache", "normalizeAppPath", "setHttpClientAndAgentOptions", "isPagesAPIRouteMatch", "BubbledError", "getTracer", "NextNodeServerSpan", "nodeFs", "getRouteRegex", "pipeToNodeResponse", "createRequestResponseMocks", "NEXT_RSC_UNION_QUERY", "signalFromNodeResponse", "loadManifest", "lazyRenderAppPage", "lazyRenderPagesPage", "interopDefault", "formatDynamicImportPath", "isInterceptionRouteRewrite", "RouteKind", "InvariantError", "Awaiter<PERSON>nce", "AsyncCallbackSet", "initializeCacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "populateStaticEnv", "isPostpone", "NodeModuleLoader", "NoFallbackError", "ensureInstrumentationRegistered", "getInstrumentationModule", "RouterServerContextSymbol", "routerServerGlobal", "dynamicImportEsmDefault", "process", "env", "NEXT_MINIMAL", "id", "then", "mod", "default", "MiddlewareMatcherCache", "WeakMap", "getMiddlewareMatcher", "info", "stored", "get", "Array", "isArray", "matchers", "Error", "JSON", "stringify", "matcher", "set", "installProcessErrorHandlers", "shouldRemoveUncaughtErrorAndRejectionListeners", "removeAllListeners", "on", "reason", "console", "error", "NextNodeServer", "constructor", "options", "registeredInstrumentation", "cleanupListeners", "handleNextImageRequest", "req", "res", "parsedUrl", "pathname", "startsWith", "minimalMode", "nextConfig", "output", "statusCode", "body", "send", "ImageOptimizerCache", "require", "imageOptimizerCache", "distDir", "sendResponse", "ImageError", "imageResponseCache", "imagesConfig", "images", "loader", "unoptimized", "render404", "paramsResult", "validateParams", "originalRequest", "query", "renderOpts", "dev", "errorMessage", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "getExtension", "previousCacheEntry", "buffer", "contentType", "maxAge", "upstreamEtag", "etag", "imageOptimizer", "value", "kind", "IMAGE", "extension", "cacheControl", "revalidate", "expire", "undefined", "routeKind", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "originalResponse", "href", "isStatic", "isMiss", "isStale", "Boolean", "err", "message", "handleCatchallRenderRequest", "relativeProjectDir", "cwd", "dir", "existingServerContext", "bind", "i18n", "i18nProvider", "fromRequest", "match", "render", "edgeFunctionsPages", "getEdgeFunctionsPages", "edgeFunctionsPage", "definition", "page", "handled", "runEdgeFunction", "params", "appPaths", "apiError", "instrumentationOnRequestError", "routePath", "routerKind", "routeType", "revalidateReason", "handleApiRequest", "formatServerError", "logErrorWithOriginalStack", "logError", "renderError", "handleCatchallMiddlewareRequest", "parsed", "isMiddlewareInvoke", "handleFinished", "middleware", "getMiddleware", "initUrl", "pathnameInfo", "normalizedPathname", "maybeDecodedPathname", "decodeURIComponent", "result", "bubblingResult", "ensureMiddleware", "url", "runMiddleware", "request", "response", "key", "Object", "entries", "headers", "<PERSON><PERSON><PERSON><PERSON>", "status", "end", "code", "finished", "isDev", "sriEnabled", "conf", "experimental", "sri", "algorithm", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "nextScriptWorkers", "__NEXT_SCRIPT_WORKERS", "NEXT_DEPLOYMENT_ID", "useSkewCookie", "deploymentId", "appDocumentPreloading", "isDefaultEnabled", "isAppPath", "catch", "preloadEntriesOnStart", "unstable_preloadEntries", "dynamicRoutes", "getRoutesManifest", "map", "r", "regex", "re", "serverOptions", "experimentalTestProxy", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "middlewareManifestPath", "serverDistDir", "prepare", "isExperimentalCompile", "removeUncaughtErrorAndRejectionListeners", "appPathsManifest", "getAppPathsManifest", "pagesManifest", "getPagesManifest", "loadCustomCacheHandlers", "keys", "ComponentMod", "patchFetch", "webpackRequire", "__next_app__", "m", "handleUpgrade", "loadInstrumentationModule", "instrumentation", "cause", "prepareImpl", "runInstrumentationHookIfAvailable", "forceReload", "silent", "cacheHandlers", "handler", "getIncrementalCache", "requestHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "getCacheFilesystem", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getResponseCache", "getPublicDir", "getHasStaticDir", "existsSync", "enabledDirectories", "app", "getinterceptionRoutePatterns", "routesManifest", "rewrites", "beforeFiles", "filter", "rewrite", "RegExp", "hasPage", "locales", "getBuildId", "buildIdFile", "readFileSync", "trim", "getEnabledDirectories", "pages", "type", "generateEtags", "poweredByHeader", "run<PERSON><PERSON>", "handledAsEdgeFunction", "parsedInitUrl", "search", "module", "load", "filename", "waitUntil", "getWaitUntil", "renderHTML", "trace", "renderHTMLImpl", "nextFontManifest", "getServerComponentsHmrCache", "buildId", "customServer", "isDraftMode", "developmentNotFoundSourcePage", "fetchExternalImage", "fetchInternalImage", "handleInternalReq", "newReq", "newRes", "routerServerHandler", "isAbsolute", "imageUpstream", "renderPageComponent", "ctx", "bubbleNoFallback", "length", "getOriginalAppPaths", "findPageComponents", "locale", "spanName", "attributes", "findPageComponentsImpl", "_url", "pagePaths", "amp", "unshift", "path", "pagePath", "components", "Component", "getStaticProps", "getNextFontManifest", "_err", "_type", "ensurePage", "_opts", "normalizeReq", "normalizeRes", "getRequestHandler", "makeRequestHandler", "wrapRequestHandlerNode", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "mocked", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "unstable_onlyGenerated", "internal", "renderToHTML", "renderErrorToResponseImpl", "is404", "clientOnly", "includes", "setHeaders", "renderErrorToHTML", "getMiddlewareManifest", "manifest", "middlewareModule", "loadNodeMiddleware", "config", "regexp", "originalSource", "functions", "getEdgeFunctionInfo", "foundPage", "pageInfo", "name", "paths", "files", "file", "wasm", "binding", "filePath", "assets", "nodeMiddleware", "functionsConfig", "hasMiddleware", "ensureEdgeFunction", "_params", "previewProps", "isOnDemandRevalidate", "Response", "skipMiddlewareUrlNormalize", "toString", "fetchHostname", "port", "middlewareInfo", "method", "toUpperCase", "requestData", "basePath", "trailingSlash", "signal", "adapterFn", "hasRequestBody", "cloneBodyStream", "finalize", "run", "edgeFunctionEntry", "useCache", "onWarning", "has", "cookies", "getSetCookie", "flatMap", "maybeCompoundCookie", "delete", "cookie", "append", "_cachedPreviewManifest", "attachRequestMeta", "isUpgradeReq", "protocol", "trustHostHeader", "host", "edgeInfo", "isNextDataRequest", "initialUrl", "URL", "queryString", "fromEntries", "searchParams", "onError", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "fetchMetrics", "statusMessage", "statusText", "for<PERSON>ach", "toLowerCase", "append<PERSON><PERSON>er", "_serverDistDir", "getFallbackErrorComponents", "args", "onServerClose", "listener", "add", "close", "runAll", "getInternalWaitUntil", "internalWaitUntil", "createInternalWaitUntil", "awaiter", "awaiting"], "mappings": "AAAA,OAAO,qBAAoB;AAC3B,OAAO,iBAAgB;AACvB,OAAO,yBAAwB;AAG/B,SACEA,WAAW,EACXC,iBAAiB,EACjBC,uBAAuB,QAClB,sBAAqB;AAiB5B,OAAOC,QAAQ,KAAI;AACnB,SAASC,IAAI,EAAEC,QAAQ,QAAQ,OAAM;AACrC,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAgB;AAC/D,SACEC,cAAc,EACdC,aAAa,EACbC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,gCAAgC,EAChCC,yBAAyB,QACpB,0BAAyB;AAChC,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,mBAAkB;AACpE,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,QAAQ,QAAQ,uCAAsC;AAC/D,YAAYC,SAAS,sBAAqB;AAa1C,OAAOC,gBAAgB,gBAAe;AACtC,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,YAAW;AACzD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,cAAc,QAAQ,oBAAmB;AAElD,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,cAAa;AAC3E,SAASC,yBAAyB,QAAQ,sDAAqD;AAC/F,SAASC,aAAa,QAAQ,YAAW;AACzC,SAASC,sBAAsB,QAAQ,yCAAwC;AAC/E,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,yBAAyB,QAAQ,cAAa;AACvD,OAAOC,iBACLC,eAAe,QAEV,mBAAkB;AACzB,SAASC,gBAAgB,QAAQ,0BAAyB;AAC1D,SAASC,gBAAgB,QAAQ,uCAAsC;AAEvE,SAASC,4BAA4B,QAAQ,yBAAwB;AAErE,SAASC,oBAAoB,QAAQ,wCAAuC;AAG5E,SAASC,YAAY,EAAEC,SAAS,QAAQ,qBAAoB;AAC5D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,MAAM,QAAQ,wBAAuB;AAC9C,SAASC,aAAa,QAAQ,yCAAwC;AACtE,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,0BAA0B,QAAQ,qBAAoB;AAC/D,SAASC,oBAAoB,QAAQ,0CAAyC;AAC9E,SAASC,sBAAsB,QAAQ,6CAA4C;AACnF,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,iBAAiB,QAAQ,yCAAwC;AAC1E,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,uBAAuB,QAAQ,oCAAmC;AAE3E,SAASC,0BAA0B,QAAQ,+CAA8C;AAEzF,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,uBAAsB;AAE/E,SAASC,iBAAiB,QAAQ,oBAAmB;AACrD,SAASC,UAAU,QAAQ,iCAAgC;AAC3D,SAASC,gBAAgB,QAAQ,yCAAwC;AACzE,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,+BAA+B,EAC/BC,wBAAwB,QACnB,sDAAqD;AAC5D,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,2CAA0C;AAEjD,cAAc,gBAAe;AAI7B,yCAAyC;AACzC,MAAMC,0BAA0BC,QAAQC,GAAG,CAACC,YAAY,GACpD,CAACC,KACC,MAAM,CAAC,uBAAuB,GAAGA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID,OACpE,CAACF,KAAe,MAAM,CAACA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAS5D,MAAME,yBAAyB,IAAIC;AAKnC,SAASC,qBACPC,IAA8C;IAE9C,MAAMC,SAASJ,uBAAuBK,GAAG,CAACF;IAC1C,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,IAAI,CAACE,MAAMC,OAAO,CAACJ,KAAKK,QAAQ,GAAG;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAEC,KAAKC,SAAS,CAACR,OAAO,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMS,UAAU9D,0BAA0BqD,KAAKK,QAAQ;IACvDR,uBAAuBa,GAAG,CAACV,MAAMS;IACjC,OAAOA;AACT;AAEA,SAASE,4BACPC,8CAAuD;IAEvD,oEAAoE;IACpE,kDAAkD;IAClD,EAAE;IACF,2EAA2E;IAC3E,2BAA2B;IAC3B,EAAE;IACF,qEAAqE;IACrE,yEAAyE;IACzE,oEAAoE;IACpE,gEAAgE;IAChE,oEAAoE;IACpE,kDAAkD;IAClD,EAAE;IACF,kCAAkC;IAClC,gDAAgD;IAChD,wBAAwB;IACxB,+CAA+C;IAC/C,QAAQ;IACR,EAAE;IACF,0EAA0E;IAC1E,qEAAqE;IACrE,yEAAyE;IACzE,iCAAiC;IACjC,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,EAAE;IACF,yEAAyE;IACzE,4BAA4B;IAC5B,EAAE;IACF,oEAAoE;IACpE,+CAA+C;IAC/C,EAAE;IACF,uEAAuE;IACvE,wDAAwD;IAExD,6EAA6E;IAC7E,8EAA8E;IAC9E,4EAA4E;IAC5E,mBAAmB;IACnB,IAAIA,gDAAgD;QAClDtB,QAAQuB,kBAAkB,CAAC;QAC3BvB,QAAQuB,kBAAkB,CAAC;IAC7B;IAEA,8DAA8D;IAC9DvB,QAAQwB,EAAE,CAAC,sBAAsB,CAACC;QAChC,IAAIjC,WAAWiC,SAAS;YACtB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,6BAA6B;QAC7B,yEAAyE;QACzE,yEAAyE;QACzE,wEAAwE;QACxE,wEAAwE;QACxE,yEAAyE;QACzE,oDAAoD;QACpDC,QAAQC,KAAK,CAACF;IAChB;IAEAzB,QAAQwB,EAAE,CAAC,oBAAoB;IAC7B,yEAAyE;IACzE,oEAAoE;IACpE,gBAAgB;IAClB;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,qDAAqD;IACrDxB,QAAQwB,EAAE,CAAC,qBAAqB,CAACC;QAC/B,IAAIjC,WAAWiC,SAAS;YACtB;QACF;QACAC,QAAQC,KAAK,CAACF;IAChB;AACF;AAEA,eAAe,MAAMG,uBAAuBjF;IAyB1CkF,YAAYC,OAAgB,CAAE;YAMFA,gCAAAA,4BAqGxBA;QA1GF,yBAAyB;QACzB,KAAK,CAACA,eAnBAC,4BAAqC,YAYnCC,mBAAmB,IAAI5C,yBA6rBvB6C,yBAA2C,OACnDC,KACAC,KACAC;YAEA,IAAI,CAACA,UAAUC,QAAQ,IAAI,CAACD,UAAUC,QAAQ,CAACC,UAAU,CAAC,iBAAiB;gBACzE,OAAO;YACT;YACA,qCAAqC;YACrC,IAAI7G,eAAeyG,KAAK,qBAAqB;gBAC3C,OAAO;YACT;YAEA,IACE,IAAI,CAACK,WAAW,IAChB,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,YAC3BzC,QAAQC,GAAG,CAACC,YAAY,EACxB;gBACAiC,IAAIO,UAAU,GAAG;gBACjBP,IAAIQ,IAAI,CAAC,eAAeC,IAAI;gBAC5B,OAAO;YACP,+CAA+C;YACjD,OAAO;gBACL,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;gBAEV,MAAMC,sBAAsB,IAAIF,oBAAoB;oBAClDG,SAAS,IAAI,CAACA,OAAO;oBACrBR,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,MAAM,EAAES,YAAY,EAAEC,UAAU,EAAE,GAChCJ,QAAQ;gBAEV,IAAI,CAAC,IAAI,CAACK,kBAAkB,EAAE;oBAC5B,MAAM,qBAAgE,CAAhE,IAAInC,MAAM,wDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+D;gBACvE;gBACA,MAAMoC,eAAe,IAAI,CAACZ,UAAU,CAACa,MAAM;gBAE3C,IAAID,aAAaE,MAAM,KAAK,aAAaF,aAAaG,WAAW,EAAE;oBACjE,MAAM,IAAI,CAACC,SAAS,CAACtB,KAAKC;oBAC1B,OAAO;gBACT;gBAEA,MAAMsB,eAAeZ,oBAAoBa,cAAc,CACrDxB,IAAIyB,eAAe,EACnBvB,UAAUwB,KAAK,EACf,IAAI,CAACpB,UAAU,EACf,CAAC,CAAC,IAAI,CAACqB,UAAU,CAACC,GAAG;gBAGvB,IAAI,kBAAkBL,cAAc;oBAClCtB,IAAIO,UAAU,GAAG;oBACjBP,IAAIQ,IAAI,CAACc,aAAaM,YAAY,EAAEnB,IAAI;oBACxC,OAAO;gBACT;gBAEA,MAAMoB,WAAWnB,oBAAoBoB,WAAW,CAACR;gBAEjD,IAAI;wBAgCES,mBAgBFA;oBA/CF,MAAM,EAAEC,YAAY,EAAE,GACpBrB,QAAQ;oBACV,MAAMoB,aAAa,MAAM,IAAI,CAACf,kBAAkB,CAACvC,GAAG,CAClDoD,UACA,OAAO,EAAEI,kBAAkB,EAAE;wBAC3B,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAE,GACvD,MAAM,IAAI,CAACC,cAAc,CACvBxC,KACAC,KACAsB,cACAW;wBAGJ,OAAO;4BACLO,OAAO;gCACLC,MAAM/G,gBAAgBgH,KAAK;gCAC3BR;gCACAI;gCACAK,WAAWX,aAAaG;gCACxBE;4BACF;4BACAO,cAAc;gCAAEC,YAAYT;gCAAQU,QAAQC;4BAAU;wBACxD;oBACF,GACA;wBACEC,WAAWlG,UAAU4F,KAAK;wBAC1BO,kBAAkBrC;wBAClBsC,YAAY;oBACd;oBAGF,IAAInB,CAAAA,+BAAAA,oBAAAA,WAAYS,KAAK,qBAAjBT,kBAAmBU,IAAI,MAAK/G,gBAAgBgH,KAAK,EAAE;wBACrD,MAAM,qBAEL,CAFK,IAAI7D,MACR,0DADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAiC,aACEf,IAAIyB,eAAe,EACnBxB,IAAImD,gBAAgB,EACpB7B,aAAa8B,IAAI,EACjBrB,WAAWS,KAAK,CAACG,SAAS,EAC1BZ,WAAWS,KAAK,CAACN,MAAM,EACvBH,WAAWS,KAAK,CAACF,IAAI,EACrBhB,aAAa+B,QAAQ,EACrBtB,WAAWuB,MAAM,GAAG,SAASvB,WAAWwB,OAAO,GAAG,UAAU,OAC5DtC,cACAc,EAAAA,2BAAAA,WAAWa,YAAY,qBAAvBb,yBAAyBc,UAAU,KAAI,GACvCW,QAAQ,IAAI,CAAC9B,UAAU,CAACC,GAAG;oBAE7B,OAAO;gBACT,EAAE,OAAO8B,KAAK;oBACZ,IAAIA,eAAe1C,YAAY;wBAC7Bf,IAAIO,UAAU,GAAGkD,IAAIlD,UAAU;wBAC/BP,IAAIQ,IAAI,CAACiD,IAAIC,OAAO,EAAEjD,IAAI;wBAC1B,OAAO;oBACT;oBACA,MAAMgD;gBACR;YACF;QACF,QAEUE,8BAAgD,OACxD5D,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEuB,KAAK,EAAE,GAAGxB;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAA6C,CAA7C,IAAIrB,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,4DAA4D;YAC5D,mDAAmD;YACnDxF,eAAe0G,KAAK,oBAAoB,IAAI,CAACK,WAAW,GAAG2C,YAAY;YAEvE,0DAA0D;YAC1D,sDAAsD;YACtD,IAAI,CAACpF,kBAAkB,CAACD,0BAA0B,EAAE;gBAClDC,kBAAkB,CAACD,0BAA0B,GAAG,CAAC;YACnD;YACA,MAAMkG,qBAAqBzK,SAAS0E,QAAQgG,GAAG,IAAI,IAAI,CAACC,GAAG;YAC3D,MAAMC,wBACJpG,kBAAkB,CAACD,0BAA0B,CAACkG,mBAAmB;YAEnE,IAAI,CAACG,uBAAuB;gBAC1BpG,kBAAkB,CAACD,0BAA0B,CAACkG,mBAAmB,GAAG;oBAClEvC,WAAW,IAAI,CAACA,SAAS,CAAC2C,IAAI,CAAC,IAAI;gBACrC;YACF;YACArG,kBAAkB,CAACD,0BAA0B,CAC3CkG,mBACD,CAACvD,UAAU,GAAG,IAAI,CAACA,UAAU;YAE9B,IAAI;oBAKM;gBAJR,wDAAwD;gBACxDH,WAAW7E,oBAAoB6E;gBAE/B,MAAMP,UAAwB;oBAC5BsE,IAAI,GAAE,qBAAA,IAAI,CAACC,YAAY,qBAAjB,mBAAmBC,WAAW,CAACpE,KAAKG;gBAC5C;gBACA,MAAMkE,QAAQ,MAAM,IAAI,CAACxF,QAAQ,CAACwF,KAAK,CAAClE,UAAUP;gBAElD,sDAAsD;gBACtD,IAAI,CAACyE,OAAO;oBACV,MAAM,IAAI,CAACC,MAAM,CAACtE,KAAKC,KAAKE,UAAUuB,OAAOxB,WAAW;oBAExD,OAAO;gBACT;gBAEA,sEAAsE;gBACtE,wBAAwB;gBACxB5G,eAAe0G,KAAK,SAASqE;gBAE7B,yCAAyC;gBACzC,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;gBACrD,KAAK,MAAMC,qBAAqBF,mBAAoB;oBAClD,6DAA6D;oBAC7D,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACC,IAAI,EAAE;oBAEjD,IAAI,IAAI,CAACrE,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACe,SAAS,CAACtB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBACA,OAAOwB,KAAK,CAACnF,qBAAqB;oBAElC,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI;wBACF,MAAMqI,UAAU,MAAM,IAAI,CAACC,eAAe,CAAC;4BACzC7E;4BACAC;4BACAyB;4BACAoD,QAAQT,MAAMS,MAAM;4BACpBH,MAAMN,MAAMK,UAAU,CAACC,IAAI;4BAC3BN;4BACAU,UAAU;wBACZ;wBACA,IAAIH,SAAS,OAAO;oBACtB,EAAE,OAAOI,UAAU;wBACjB,MAAM,IAAI,CAACC,6BAA6B,CAACD,UAAUhF,KAAK;4BACtDkF,WAAWb,MAAMK,UAAU,CAACC,IAAI;4BAChCQ,YAAY;4BACZC,WAAW;4BACX,oCAAoC;4BACpCC,kBAAkBrC;wBACpB;wBACA,MAAMgC;oBACR;gBACF;gBAEA,oEAAoE;gBACpE,MAAM;gBACN,iDAAiD;gBACjD,IAAIjJ,qBAAqBsI,QAAQ;oBAC/B,IAAI,IAAI,CAAC/D,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACe,SAAS,CAACtB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBAEA,MAAM0E,UAAU,MAAM,IAAI,CAACU,gBAAgB,CAACtF,KAAKC,KAAKyB,OAAO2C;oBAC7D,IAAIO,SAAS,OAAO;gBACtB;gBAEA,MAAM,IAAI,CAACN,MAAM,CAACtE,KAAKC,KAAKE,UAAUuB,OAAOxB,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOwD,KAAU;gBACjB,IAAIA,eAAelG,iBAAiB;oBAClC,MAAMkG;gBACR;gBAEA,IAAI;oBACF,IAAI,IAAI,CAAC/B,UAAU,CAACC,GAAG,EAAE;wBACvB,MAAM,EAAE2D,iBAAiB,EAAE,GACzB3E,QAAQ;wBACV2E,kBAAkB7B;wBAClB,IAAI,CAAC8B,yBAAyB,CAAC9B;oBACjC,OAAO;wBACL,IAAI,CAAC+B,QAAQ,CAAC/B;oBAChB;oBACAzD,IAAIO,UAAU,GAAG;oBACjB,MAAM,IAAI,CAACkF,WAAW,CAAChC,KAAK1D,KAAKC,KAAKE,UAAUuB;oBAChD,OAAO;gBACT,EAAE,OAAM,CAAC;gBAET,MAAMgC;YACR;QACF,QAmjBUiC,kCAAoD,OAC5D3F,KACAC,KACA2F;YAEA,MAAMC,qBAAqBtM,eAAeyG,KAAK;YAE/C,IAAI,CAAC6F,oBAAoB;gBACvB,OAAO;YACT;YAEA,MAAMC,iBAAiB;gBACrBxM,eAAe0G,KAAK,oBAAoB;gBACxCC,IAAIQ,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;YAEA,MAAMqF,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,IAAI,CAACD,YAAY;gBACf,OAAOD;YACT;YAEA,MAAMG,UAAU1M,eAAeyG,KAAK;YACpC,MAAME,YAAY3F,SAAS0L;YAC3B,MAAMC,eAAe3K,oBAAoB2E,UAAUC,QAAQ,EAAE;gBAC3DG,YAAY,IAAI,CAACA,UAAU;gBAC3B6D,cAAc,IAAI,CAACA,YAAY;YACjC;YAEAjE,UAAUC,QAAQ,GAAG+F,aAAa/F,QAAQ;YAC1C,MAAMgG,qBAAqB7K,oBAAoBsK,OAAOzF,QAAQ,IAAI;YAClE,IAAIiG,uBAAuBD;YAE3B,IAAI;gBACFC,uBAAuBC,mBAAmBF;YAC5C,EAAE,OAAM;YACN,+CAA+C,GACjD;YAEA,IACE,CACEJ,CAAAA,WAAW1B,KAAK,CAAC8B,oBAAoBnG,KAAKE,UAAUwB,KAAK,KACzDqE,WAAW1B,KAAK,CAAC+B,sBAAsBpG,KAAKE,UAAUwB,KAAK,CAAA,GAE7D;gBACA,OAAOoE;YACT;YAEA,IAAIQ;YAGJ,IAAIC,iBAAiB;YAErB,IAAI;gBACF,MAAM,IAAI,CAACC,gBAAgB,CAACxG,IAAIyG,GAAG;gBAEnCH,SAAS,MAAM,IAAI,CAACI,aAAa,CAAC;oBAChCC,SAAS3G;oBACT4G,UAAU3G;oBACVC,WAAWA;oBACX0F,QAAQA;gBACV;gBAEA,IAAI,cAAcU,QAAQ;oBACxB,IAAIT,oBAAoB;wBACtBU,iBAAiB;wBACjB,MAAM,qBAA8B,CAA9B,IAAIvK,aAAa,MAAMsK,SAAvB,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC;oBAEA,KAAK,MAAM,CAACO,KAAKpE,MAAM,IAAIqE,OAAOC,OAAO,CACvC7L,0BAA0BoL,OAAOM,QAAQ,CAACI,OAAO,GAChD;wBACD,IAAIH,QAAQ,sBAAsBpE,UAAUO,WAAW;4BACrD/C,IAAIgH,SAAS,CAACJ,KAAKpE;wBACrB;oBACF;oBACAxC,IAAIO,UAAU,GAAG8F,OAAOM,QAAQ,CAACM,MAAM;oBAEvC,MAAM,EAAE9D,gBAAgB,EAAE,GAAGnD;oBAC7B,IAAIqG,OAAOM,QAAQ,CAACnG,IAAI,EAAE;wBACxB,MAAMpE,mBAAmBiK,OAAOM,QAAQ,CAACnG,IAAI,EAAE2C;oBACjD,OAAO;wBACLA,iBAAiB+D,GAAG;oBACtB;oBACA,OAAO;gBACT;YACF,EAAE,OAAOzD,KAAc;gBACrB,IAAI6C,gBAAgB;oBAClB,MAAM7C;gBACR;gBAEA,IAAI3I,QAAQ2I,QAAQA,IAAI0D,IAAI,KAAK,UAAU;oBACzC,MAAM,IAAI,CAAC9F,SAAS,CAACtB,KAAKC,KAAK2F;oBAC/B,OAAO;gBACT;gBAEA,IAAIlC,eAAe3K,aAAa;oBAC9BkH,IAAIO,UAAU,GAAG;oBACjB,MAAM,IAAI,CAACkF,WAAW,CAAChC,KAAK1D,KAAKC,KAAK2F,OAAOzF,QAAQ,IAAI;oBACzD,OAAO;gBACT;gBAEA,MAAMV,QAAQzE,eAAe0I;gBAC7BlE,QAAQC,KAAK,CAACA;gBACdQ,IAAIO,UAAU,GAAG;gBACjB,MAAM,IAAI,CAACkF,WAAW,CAACjG,OAAOO,KAAKC,KAAK2F,OAAOzF,QAAQ,IAAI;gBAC3D,OAAO;YACT;YAEA,OAAOmG,OAAOe,QAAQ;QACxB;QA7kDE,MAAMC,QAAQ1H,QAAQgC,GAAG,IAAI;QAC7B,IAAI,CAAC0F,KAAK,GAAGA;QACb,IAAI,CAACC,UAAU,GAAG9D,SAAQ7D,6BAAAA,QAAQ4H,IAAI,CAACC,YAAY,sBAAzB7H,iCAAAA,2BAA2B8H,GAAG,qBAA9B9H,+BAAgC+H,SAAS;QAEnE;;;;KAIC,GACD,IAAI,IAAI,CAAChG,UAAU,CAACiG,WAAW,EAAE;YAC/B9J,QAAQC,GAAG,CAAC8J,mBAAmB,GAAG9I,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI,IAAI,CAAC2C,UAAU,CAACmG,iBAAiB,EAAE;YACrChK,QAAQC,GAAG,CAACgK,qBAAqB,GAAGhJ,KAAKC,SAAS,CAAC;QACrD;QACAlB,QAAQC,GAAG,CAACiK,kBAAkB,GAAG,IAAI,CAAC1H,UAAU,CAACmH,YAAY,CAACQ,aAAa,GACvE,KACA,IAAI,CAAC3H,UAAU,CAAC4H,YAAY,IAAI;QAEpC,IAAI,CAAC,IAAI,CAAC7H,WAAW,EAAE;YACrB,IAAI,CAACY,kBAAkB,GAAG,IAAIvF,cAAc,IAAI,CAAC2E,WAAW;QAC9D;QAEA,MAAM,EAAE8H,qBAAqB,EAAE,GAAG,IAAI,CAAC7H,UAAU,CAACmH,YAAY;QAC9D,MAAMW,mBAAmB,OAAOD,0BAA0B;QAE1D,IACE,CAACvI,QAAQgC,GAAG,IACXuG,CAAAA,0BAA0B,QACzB,CAAE,CAAA,IAAI,CAAC9H,WAAW,IAAI+H,gBAAe,CAAC,GACxC;YACA,+CAA+C;YAC/C,2BAA2B;YAC3BtN,eAAe;gBACbgG,SAAS,IAAI,CAACA,OAAO;gBACrB6D,MAAM;gBACN0D,WAAW;gBACXf,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGe,KAAK,CAAC,KAAO;YAChBxN,eAAe;gBACbgG,SAAS,IAAI,CAACA,OAAO;gBACrB6D,MAAM;gBACN0D,WAAW;gBACXf,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGe,KAAK,CAAC,KAAO;QAClB;QAEA,IACE,CAAC1I,QAAQgC,GAAG,IACZ,CAAC,IAAI,CAACvB,WAAW,IACjB,IAAI,CAACC,UAAU,CAACmH,YAAY,CAACc,qBAAqB,EAClD;YACA,IAAI,CAACC,uBAAuB;QAC9B;QAEA,IAAI,CAAC5I,QAAQgC,GAAG,EAAE;YAChB,MAAM,EAAE6G,gBAAgB,EAAE,EAAE,GAAG,IAAI,CAACC,iBAAiB,MAAM,CAAC;YAC5D,IAAI,CAACD,aAAa,GAAGA,cAAcE,GAAG,CAAC,CAACC;gBACtC,wDAAwD;gBACxD,MAAMC,QAAQzM,cAAcwM,EAAEjE,IAAI;gBAClC,MAAMN,QAAQhL,gBAAgBwP;gBAE9B,OAAO;oBACLxE;oBACAM,MAAMiE,EAAEjE,IAAI;oBACZmE,IAAID,MAAMC,EAAE;gBACd;YACF;QACF;QAEA,sDAAsD;QACtDhN,6BAA6B,IAAI,CAACwE,UAAU;QAE5C,2CAA2C;QAC3C,IAAI,IAAI,CAACyI,aAAa,CAACC,qBAAqB,EAAE;YAC5ClL,QAAQC,GAAG,CAACkL,uBAAuB,GAAG;YACtC,MAAM,EAAEC,iBAAiB,EAAE,GACzB,sHAAsH;YACtHtI,QAAQ;YACVsI;QACF;QAEA,IAAI,CAACC,sBAAsB,GAAGhQ,KAAK,IAAI,CAACiQ,aAAa,EAAE1P;QAEvD,4EAA4E;QAC5E,2EAA2E;QAC3E,qEAAqE;QACrE,uEAAuE;QACvE,IAAI,CAACkG,QAAQgC,GAAG,EAAE;YAChB,IAAI,CAACyH,OAAO,GAAGf,KAAK,CAAC,CAAC5E;gBACpBlE,QAAQC,KAAK,CAAC,4BAA4BiE;YAC5C;QACF;QAEA,yDAAyD;QACzD,yCAAyC;QACzC,IAAI,IAAI,CAAC/B,UAAU,CAAC2H,qBAAqB,EAAE;YACzCjM,kBAAkB,IAAI,CAACiD,UAAU;QACnC;QAEA,MAAMlB,iDAAiDqE,SACrD7D,8BAAAA,QAAQ4H,IAAI,CAACC,YAAY,qBAAzB7H,4BAA2B2J,wCAAwC;QAErEpK,4BAA4BC;IAC9B;IAEA,MAAaoJ,0BAAyC;QACpD,qEAAqE;QACrE,MAAM,IAAI,CAACa,OAAO;QAElB,MAAMG,mBAAmB,IAAI,CAACC,mBAAmB;QACjD,MAAMC,gBAAgB,IAAI,CAACC,gBAAgB;QAE3C,MAAM,IAAI,CAACC,uBAAuB;QAElC,KAAK,MAAMjF,QAAQmC,OAAO+C,IAAI,CAACH,iBAAiB,CAAC,GAAI;YACnD,MAAM5O,eAAe;gBACnBgG,SAAS,IAAI,CAACA,OAAO;gBACrB6D;gBACA0D,WAAW;gBACXf,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGe,KAAK,CAAC,KAAO;QAClB;QAEA,KAAK,MAAM3D,QAAQmC,OAAO+C,IAAI,CAACL,oBAAoB,CAAC,GAAI;YACtD,MAAM1O,eAAe;gBACnBgG,SAAS,IAAI,CAACA,OAAO;gBACrB6D;gBACA0D,WAAW;gBACXf,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GACGrJ,IAAI,CAAC,OAAO,EAAE4L,YAAY,EAAE;gBAC3B,iEAAiE;gBACjE,yEAAyE;gBACzE,oDAAoD;gBACpDA,aAAaC,UAAU;gBAEvB,MAAMC,iBAAiBF,aAAaG,YAAY,CAACrJ,OAAO;gBACxD,IAAIoJ,kCAAAA,eAAgBE,CAAC,EAAE;oBACrB,KAAK,MAAMjM,MAAM6I,OAAO+C,IAAI,CAACG,eAAeE,CAAC,EAAG;wBAC9C,MAAMF,eAAe/L;oBACvB;gBACF;YACF,GACCqK,KAAK,CAAC,KAAO;QAClB;IACF;IAEA,MAAgB6B,gBAA+B;IAC7C,yEAAyE;IACzE,eAAe;IACjB;IAEA,MAAgBC,4BAA4B;QAC1C,IAAI,CAAC,IAAI,CAACrB,aAAa,CAACnH,GAAG,EAAE;YAC3B,IAAI;gBACF,IAAI,CAACyI,eAAe,GAAG,MAAM3M,yBAC3B,IAAI,CAACqG,GAAG,EACR,IAAI,CAACzD,UAAU,CAACQ,OAAO;YAE3B,EAAE,OAAO4C,KAAU;gBACjB,IAAIA,IAAI0D,IAAI,KAAK,oBAAoB;oBACnC,MAAM,qBAGL,CAHK,IAAItI,MACR,4DACA;wBAAEwL,OAAO5G;oBAAI,IAFT,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QACA,OAAO,IAAI,CAAC2G,eAAe;IAC7B;IAEA,MAAgBE,cAAc;QAC5B,MAAM,KAAK,CAACA;QACZ,MAAM,IAAI,CAACC,iCAAiC;IAC9C;IAEA,MAAgBA,oCAAoC;QAClD,MAAM/M,gCAAgC,IAAI,CAACsG,GAAG,EAAE,IAAI,CAACzD,UAAU,CAACQ,OAAO;IACzE;IAEU1F,cAAc,EACtBwG,GAAG,EACH6I,WAAW,EACXC,MAAM,EAKP,EAAE;QACDtP,cACE,IAAI,CAAC2I,GAAG,EACRnC,KACA8I,SAAS;YAAElM,MAAM,KAAO;YAAGiB,OAAO,KAAO;QAAE,IAAIjF,KAC/CiQ;IAEJ;IAEA,MAAcb,0BAA0B;QACtC,MAAM,EAAEe,aAAa,EAAE,GAAG,IAAI,CAACrK,UAAU,CAACmH,YAAY;QACtD,IAAI,CAACkD,eAAe;QAEpB,yEAAyE;QACzE,SAAS;QACT,IAAI,CAACxN,2BAA2B;QAEhC,KAAK,MAAM,CAACuF,MAAMkI,QAAQ,IAAI9D,OAAOC,OAAO,CAAC4D,eAAgB;YAC3D,IAAI,CAACC,SAAS;YAEdxN,gBACEsF,MACA9F,eACE,MAAMiB,wBACJhB,wBAAwB,IAAI,CAACiE,OAAO,EAAE8J;QAI9C;IACF;IAEA,MAAgBC,oBAAoB,EAClCC,cAAc,EAGf,EAAE;QACD,MAAMlJ,MAAM,CAAC,CAAC,IAAI,CAACD,UAAU,CAACC,GAAG;QACjC,IAAImJ;QACJ,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAI,CAAC1K,UAAU;QAExC,IAAI0K,cAAc;YAChBD,eAAenO,eACb,MAAMiB,wBACJhB,wBAAwB,IAAI,CAACiE,OAAO,EAAEkK;QAG5C;QAEA,MAAM,IAAI,CAACpB,uBAAuB;QAElC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIhO,iBAAiB;YAC1B1C,IAAI,IAAI,CAAC+R,kBAAkB;YAC3BrJ;YACAkJ;YACAI,6BACE,IAAI,CAAC5K,UAAU,CAACmH,YAAY,CAACyD,2BAA2B;YAC1D7K,aAAa,IAAI,CAACA,WAAW;YAC7B+I,eAAe,IAAI,CAACA,aAAa;YACjC+B,qBAAqB,IAAI,CAAC7K,UAAU,CAACmH,YAAY,CAAC0D,mBAAmB;YACrEC,oBAAoB,IAAI,CAAC9K,UAAU,CAAC+K,kBAAkB;YACtDC,aACE,CAAC,IAAI,CAACjL,WAAW,IAAI,IAAI,CAACC,UAAU,CAACmH,YAAY,CAAC8D,cAAc;YAClEC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrDC,iBAAiBV;QACnB;IACF;IAEUW,mBAAmB;QAC3B,OAAO,IAAIhQ,cAAc,IAAI,CAAC2E,WAAW;IAC3C;IAEUsL,eAAuB;QAC/B,OAAOxS,KAAK,IAAI,CAAC4K,GAAG,EAAElK;IACxB;IAEU+R,kBAA2B;QACnC,OAAO1S,GAAG2S,UAAU,CAAC1S,KAAK,IAAI,CAAC4K,GAAG,EAAE;IACtC;IAEU4F,mBAA8C;QACtD,OAAOlN,aACLtD,KAAK,IAAI,CAACiQ,aAAa,EAAE5P;IAE7B;IAEUiQ,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACqC,kBAAkB,CAACC,GAAG,EAAE,OAAO/I;QAEzC,OAAOvG,aACLtD,KAAK,IAAI,CAACiQ,aAAa,EAAEtP;IAE7B;IAEUkS,+BAAyC;QACjD,IAAI,CAAC,IAAI,CAACF,kBAAkB,CAACC,GAAG,EAAE,OAAO,EAAE;QAE3C,MAAME,iBAAiB,IAAI,CAACvD,iBAAiB;QAC7C,OACEuD,CAAAA,kCAAAA,eAAgBC,QAAQ,CAACC,WAAW,CACjCC,MAAM,CAACtP,4BACP6L,GAAG,CAAC,CAAC0D,UAAY,IAAIC,OAAOD,QAAQxD,KAAK,OAAM,EAAE;IAExD;IAEA,MAAgB0D,QAAQpM,QAAgB,EAAoB;YAIxD;QAHF,OAAO,CAAC,CAACzF,iBACPyF,UACA,IAAI,CAACW,OAAO,GACZ,wBAAA,IAAI,CAACR,UAAU,CAAC4D,IAAI,qBAApB,sBAAsBsI,OAAO,EAC7B,IAAI,CAACV,kBAAkB,CAACC,GAAG;IAE/B;IAEUU,aAAqB;QAC7B,MAAMC,cAAcvT,KAAK,IAAI,CAAC2H,OAAO,EAAErH;QACvC,IAAI;YACF,OAAOP,GAAGyT,YAAY,CAACD,aAAa,QAAQE,IAAI;QAClD,EAAE,OAAOlJ,KAAU;YACjB,IAAIA,IAAI0D,IAAI,KAAK,UAAU;gBACzB,MAAM,qBAEL,CAFK,IAAItI,MACR,CAAC,0CAA0C,EAAE,IAAI,CAACgC,OAAO,CAAC,yJAAyJ,CAAC,GADhN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM4C;QACR;IACF;IAEUmJ,sBAAsBjL,GAAY,EAA0B;QACpE,MAAMmC,MAAMnC,MAAM,IAAI,CAACmC,GAAG,GAAG,IAAI,CAACqF,aAAa;QAE/C,OAAO;YACL2C,KAAK5R,QAAQ4J,KAAK,SAAS,OAAO;YAClC+I,OAAO3S,QAAQ4J,KAAK,WAAW,OAAO;QACxC;IACF;IAEUzJ,iBACR0F,GAAoB,EACpBC,GAAqB,EACrBL,OAMC,EACc;QACf,OAAOtF,iBAAiB;YACtB0F,KAAKA,IAAIyB,eAAe;YACxBxB,KAAKA,IAAImD,gBAAgB;YACzBkD,QAAQ1G,QAAQ0G,MAAM;YACtByG,MAAMnN,QAAQmN,IAAI;YAClBC,eAAepN,QAAQoN,aAAa;YACpCC,iBAAiBrN,QAAQqN,eAAe;YACxCpK,cAAcjD,QAAQiD,YAAY;QACpC;IACF;IAEA,MAAgBqK,OACdlN,GAAoB,EACpBC,GAAqB,EACrByB,KAAqB,EACrB2C,KAAyB,EACP;QAClB,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;QAErD,KAAK,MAAMC,qBAAqBF,mBAAoB;YAClD,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACvE,QAAQ,EAAE;gBACnD,MAAMgN,wBAAwB,MAAM,IAAI,CAACtI,eAAe,CAAC;oBACvD7E;oBACAC;oBACAyB;oBACAoD,QAAQT,MAAMS,MAAM;oBACpBH,MAAMN,MAAMK,UAAU,CAACvE,QAAQ;oBAC/B4E,UAAU;gBACZ;gBAEA,IAAIoI,uBAAuB;oBACzB,OAAO;gBACT;YACF;QACF;QACA,6DAA6D;QAC7D,+DAA+D;QAC/D,MAAMC,gBAAgB7S,SAAShB,eAAeyG,KAAK,cAAcA,IAAIyG,GAAG;QACxEzG,IAAIyG,GAAG,GAAG,GAAG2G,cAAcjN,QAAQ,GAAGiN,cAAcC,MAAM,IAAI,IAAI;QAElE,MAAMjM,SAAS,IAAI7D;QACnB,MAAM+P,SAAU,MAAMlM,OAAOmM,IAAI,CAAClJ,MAAMK,UAAU,CAAC8I,QAAQ;QAS3DlU,eAAe0G,IAAIyB,eAAe,EAAE,cAAc,IAAI,CAACsC,GAAG;QAC1DzK,eAAe0G,IAAIyB,eAAe,EAAE,WAAW,IAAI,CAACX,OAAO;QAC3D,MAAMwM,OAAO1C,OAAO,CAAC5K,IAAIyB,eAAe,EAAExB,IAAImD,gBAAgB,EAAE;YAC9DqK,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,OAAO;IACT;IAEA,MAAgBC,WACd3N,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBuB,KAAyB,EACzBC,UAA4B,EACL;QACvB,OAAO1F,YAAY2R,KAAK,CAAC1R,mBAAmByR,UAAU,EAAE,UACtD,IAAI,CAACE,cAAc,CAAC7N,KAAKC,KAAKE,UAAUuB,OAAOC;IAEnD;IAEA,MAAckM,eACZ7N,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBuB,KAAyB,EACzBC,UAA4B,EACL;QACvB,IAAI7D,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACA,+CAA+C;QACjD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9D,4HAA4H;YAC5H6C,WAAWmM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAEnD,IAAI,IAAI,CAAChC,kBAAkB,CAACC,GAAG,IAAIpK,WAAW0G,SAAS,EAAE;gBACvD,OAAO3L,kBACLsD,KACAC,KACAE,UACAuB,OACA,kEAAkE;gBAClE,oEAAoE;gBACpE,MACAC,YACA,IAAI,CAACoM,2BAA2B,IAChC,OACA;oBACEC,SAAS,IAAI,CAACA,OAAO;gBACvB;YAEJ;YAEA,qEAAqE;YACrE,oEAAoE;YAEpE,OAAOrR,oBACLqD,IAAIyB,eAAe,EACnBxB,IAAImD,gBAAgB,EACpBjD,UACAuB,OACAC,YACA;gBACEqM,SAAS,IAAI,CAACA,OAAO;gBACrB9F,cAAc,IAAI,CAAC5H,UAAU,CAAC4H,YAAY;gBAC1C+F,cAAc,IAAI,CAAClF,aAAa,CAACkF,YAAY,IAAIjL;YACnD,GACA;gBACEG,YAAY;gBACZ+K,aAAavM,WAAWuM,WAAW;gBACnCC,+BAA+B5U,eAC7ByG,KACA;YAEJ;QAEJ;IACF;IAEA,MAAgBwC,eACdxC,GAAoB,EACpBC,GAAqB,EACrBsB,YAA2D,EAC3DW,kBAAyD,EAOxD;QACD,IAAIpE,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACL,MAAM,EAAE0D,cAAc,EAAE4L,kBAAkB,EAAEC,kBAAkB,EAAE,GAC9DzN,QAAQ;YAEV,MAAM0N,oBAAoB,OACxBC,QACAC;gBAEA,IAAID,OAAO9H,GAAG,KAAKzG,IAAIyG,GAAG,EAAE;oBAC1B,MAAM,qBAA+D,CAA/D,IAAI3H,MAAM,CAAC,kDAAkD,CAAC,GAA9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA8D;gBACtE;gBAEA,IAAI,CAAC,IAAI,CAAC2P,mBAAmB,EAAE;oBAC7B,MAAM,qBAAkD,CAAlD,IAAI3P,MAAM,CAAC,qCAAqC,CAAC,GAAjD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiD;gBACzD;gBAEA,MAAM,IAAI,CAAC2P,mBAAmB,CAACF,QAAQC;gBACvC;YACF;YAEA,MAAM,EAAEE,UAAU,EAAErL,IAAI,EAAE,GAAG9B;YAE7B,MAAMoN,gBAAgBD,aAClB,MAAMN,mBAAmB/K,QACzB,MAAMgL,mBACJhL,MACArD,IAAIyB,eAAe,EACnBxB,IAAImD,gBAAgB,EACpBkL;YAGN,OAAO9L,eAAemM,eAAepN,cAAc,IAAI,CAACjB,UAAU,EAAE;gBAClEgH,OAAO,IAAI,CAAC3F,UAAU,CAACC,GAAG;gBAC1BM;YACF;QACF;IACF;IAEUvH,YAAYwF,QAAgB,EAAEqM,OAAkB,EAAU;QAClE,OAAO7R,YACLwF,UACA,IAAI,CAACW,OAAO,EACZ0L,SACA,IAAI,CAACV,kBAAkB,CAACC,GAAG;IAE/B;IAEA,MAAgB6C,oBACdC,GAAsD,EACtDC,gBAAyB,EACzB;QACA,MAAMvK,qBAAqB,IAAI,CAACC,qBAAqB,MAAM,EAAE;QAC7D,IAAID,mBAAmBwK,MAAM,EAAE;YAC7B,MAAMhK,WAAW,IAAI,CAACiK,mBAAmB,CAACH,IAAI1O,QAAQ;YACtD,MAAMkI,YAAY1J,MAAMC,OAAO,CAACmG;YAEhC,IAAIJ,OAAOkK,IAAI1O,QAAQ;YACvB,IAAIkI,WAAW;gBACb,yEAAyE;gBACzE1D,OAAOI,QAAQ,CAAC,EAAE;YACpB;YAEA,KAAK,MAAMN,qBAAqBF,mBAAoB;gBAClD,IAAIE,sBAAsBE,MAAM;oBAC9B,MAAM,IAAI,CAACE,eAAe,CAAC;wBACzB7E,KAAK6O,IAAI7O,GAAG;wBACZC,KAAK4O,IAAI5O,GAAG;wBACZyB,OAAOmN,IAAInN,KAAK;wBAChBoD,QAAQ+J,IAAIlN,UAAU,CAACmD,MAAM;wBAC7BH;wBACAI;oBACF;oBACA,OAAO;gBACT;YACF;QACF;QAEA,OAAO,KAAK,CAAC6J,oBAAoBC,KAAKC;IACxC;IAEA,MAAgBG,mBAAmB,EACjCC,MAAM,EACNvK,IAAI,EACJjD,KAAK,EACLoD,MAAM,EACNuD,SAAS,EACT5B,GAAG,EAaJ,EAAwC;QACvC,OAAOxK,YAAY2R,KAAK,CACtB1R,mBAAmB+S,kBAAkB,EACrC;YACEE,UAAU;YACVC,YAAY;gBACV,cAAc/G,YAAYxM,iBAAiB8I,QAAQA;YACrD;QACF,GACA,IACE,IAAI,CAAC0K,sBAAsB,CAAC;gBAC1BH;gBACAvK;gBACAjD;gBACAoD;gBACAuD;gBACA5B;YACF;IAEN;IAEA,MAAc4I,uBAAuB,EACnCH,MAAM,EACNvK,IAAI,EACJjD,KAAK,EACLoD,MAAM,EACNuD,SAAS,EACT5B,KAAK6I,IAAI,EAQV,EAAwC;QACvC,MAAMC,YAAsB;YAAC5K;SAAK;QAClC,IAAIjD,MAAM8N,GAAG,EAAE;YACb,yCAAyC;YACzCD,UAAUE,OAAO,CACf,AAACpH,CAAAA,YAAYxM,iBAAiB8I,QAAQ9J,kBAAkB8J,KAAI,IAAK;QAErE;QAEA,IAAIuK,QAAQ;YACVK,UAAUE,OAAO,IACZF,UAAU5G,GAAG,CAAC,CAAC+G,OAAS,CAAC,CAAC,EAAER,SAASQ,SAAS,MAAM,KAAKA,MAAM;QAEtE;QAEA,KAAK,MAAMC,YAAYJ,UAAW;YAChC,IAAI;gBACF,MAAMK,aAAa,MAAM9U,eAAe;oBACtCgG,SAAS,IAAI,CAACA,OAAO;oBACrB6D,MAAMgL;oBACNtH;oBACAf,OAAO,IAAI,CAACA,KAAK;oBACjBC,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,IACE2H,UACA,OAAOU,WAAWC,SAAS,KAAK,YAChC,CAACF,SAASvP,UAAU,CAAC,CAAC,CAAC,EAAE8O,OAAO,CAAC,CAAC,KAClCS,aAAa,CAAC,CAAC,EAAET,QAAQ,EACzB;oBAGA;gBACF;gBAEA,OAAO;oBACLU;oBACAlO,OAAO;wBACL,GAAI,CAAC,IAAI,CAACC,UAAU,CAAC2H,qBAAqB,IAC1CsG,WAAWE,cAAc,GACpB;4BACCN,KAAK9N,MAAM8N,GAAG;wBAChB,IACA9N,KAAK;wBACT,iCAAiC;wBACjC,GAAI,AAAC2G,CAAAA,YAAY,CAAC,IAAIvD,MAAK,KAAM,CAAC,CAAC;oBACrC;gBACF;YACF,EAAE,OAAOpB,KAAK;gBACZ,yDAAyD;gBACzD,wBAAwB;gBACxB,IAAI,CAAEA,CAAAA,eAAe1K,iBAAgB,GAAI;oBACvC,MAAM0K;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEUqM,sBAAoD;QAC5D,OAAOtT,aACLtD,KAAK,IAAI,CAAC2H,OAAO,EAAE,UAAU9G,qBAAqB;IAEtD;IA4PA,0DAA0D;IAChDwL,0BACRwK,IAAc,EACdC,KAA0E,EACpE;QACN,MAAM,qBAEL,CAFK,IAAInR,MACR,sFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,0DAA0D;IAC1D,MAAgBoR,WAAWC,KAM1B,EAAiB;QAChB,MAAM,qBAEL,CAFK,IAAIrR,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA;;;;;GAKC,GACD,MAAgBwG,iBACdtF,GAAoB,EACpBC,GAAqB,EACrByB,KAAqB,EACrB2C,KAAyB,EACP;QAClB,OAAO,IAAI,CAAC6I,MAAM,CAAClN,KAAKC,KAAKyB,OAAO2C;IACtC;IAEU4G,qBAA8B;QACtC,OAAO9O;IACT;IAEUiU,aACRpQ,GAAsC,EACrB;QACjB,OAAO,CAAEA,CAAAA,eAAe5F,eAAc,IAAK,IAAIA,gBAAgB4F,OAAOA;IACxE;IAEUqQ,aACRpQ,GAAsC,EACpB;QAClB,OAAO,CAAEA,CAAAA,eAAe5F,gBAAe,IAAK,IAAIA,iBAAiB4F,OAAOA;IAC1E;IAEOqQ,oBAAwC;QAC7C,MAAM1F,UAAU,IAAI,CAAC2F,kBAAkB;QACvC,IAAI,IAAI,CAACxH,aAAa,CAACC,qBAAqB,EAAE;YAC5C,MAAM,EAAEwH,sBAAsB,EAAE,GAC9B,sHAAsH;YACtH5P,QAAQ;YACV,OAAO4P,uBAAuB5F;QAChC;QACA,OAAOA;IACT;IAEQ2F,qBAAyC;QAC/C,4EAA4E;QAC5E,2EAA2E;QAC3E,sEAAsE;QACtE,uEAAuE;QACvE,IAAI,CAAClH,OAAO,GAAGf,KAAK,CAAC,CAAC5E;YACpBlE,QAAQC,KAAK,CAAC,4BAA4BiE;QAC5C;QAEA,MAAMkH,UAAU,KAAK,CAAC0F;QAEtB,OAAO,CAACtQ,KAAKC,KAAKC,YAChB0K,QAAQ,IAAI,CAACwF,YAAY,CAACpQ,MAAM,IAAI,CAACqQ,YAAY,CAACpQ,MAAMC;IAC5D;IAEA,MAAa4C,WAAW,EACtB2N,OAAO,EACPC,iBAAiB,EACjBC,IAAI,EAKL,EAAE;QACD,MAAMC,SAAStU,2BAA2B;YACxCmK,KAAKgK;YACLzJ,SAAS0J;QACX;QAEA,MAAM9F,UAAU,IAAI,CAAC0F,iBAAiB;QACtC,MAAM1F,QACJ,IAAIxQ,gBAAgBwW,OAAO5Q,GAAG,GAC9B,IAAI3F,iBAAiBuW,OAAO3Q,GAAG;QAEjC,MAAM2Q,OAAO3Q,GAAG,CAAC4Q,WAAW;QAE5B,IACED,OAAO3Q,GAAG,CAAC6Q,SAAS,CAAC,sBAAsB,iBAC3CF,OAAO3Q,GAAG,CAACO,UAAU,KAAK,OAC1B,CAAEoQ,CAAAA,OAAO3Q,GAAG,CAACO,UAAU,KAAK,OAAOmQ,KAAKI,sBAAsB,AAAD,GAC7D;YACA,MAAM,qBAAsD,CAAtD,IAAIjS,MAAM,CAAC,iBAAiB,EAAE8R,OAAO3Q,GAAG,CAACO,UAAU,EAAE,GAArD,qBAAA;uBAAA;4BAAA;8BAAA;YAAqD;QAC7D;IACF;IAEA,MAAa8D,OACXtE,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAA0B,EAC1BxB,SAAkC,EAClC8Q,WAAW,KAAK,EACD;QACf,OAAO,KAAK,CAAC1M,OACX,IAAI,CAAC8L,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAuB,OACAxB,WACA8Q;IAEJ;IAEA,MAAaC,aACXjR,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACuP,aACX,IAAI,CAACb,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAuB;IAEJ;IAEA,MAAgBwP,0BACdrC,GAAsD,EACtDnL,GAAiB,EACjB;QACA,MAAM,EAAE1D,GAAG,EAAEC,GAAG,EAAEyB,KAAK,EAAE,GAAGmN;QAC5B,MAAMsC,QAAQlR,IAAIO,UAAU,KAAK;QAEjC,IAAI2Q,SAAS,IAAI,CAACrF,kBAAkB,CAACC,GAAG,EAAE;YACxC,IAAI,IAAI,CAACpK,UAAU,CAACC,GAAG,EAAE;gBACvB,MAAM,IAAI,CAACsO,UAAU,CAAC;oBACpBvL,MAAM1K;oBACNmX,YAAY;oBACZ3K,KAAKzG,IAAIyG,GAAG;gBACd,GAAG6B,KAAK,CAAC,KAAO;YAClB;YAEA,IACE,IAAI,CAAC9D,qBAAqB,GAAG6M,QAAQ,CAACpX,mCACtC;gBACA,MAAM,IAAI,CAAC4K,eAAe,CAAC;oBACzB7E;oBACAC;oBACAyB,OAAOA,SAAS,CAAC;oBACjBoD,QAAQ,CAAC;oBACTH,MAAM1K;oBACN8K,UAAU;gBACZ;gBACA,OAAO;YACT;QACF;QACA,OAAO,KAAK,CAACmM,0BAA0BrC,KAAKnL;IAC9C;IAEA,MAAagC,YACXhC,GAAiB,EACjB1D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAA0B,EAC1B4P,UAAoB,EACL;QACf,OAAO,KAAK,CAAC5L,YACXhC,KACA,IAAI,CAAC0M,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAuB,OACA4P;IAEJ;IAEA,MAAaC,kBACX7N,GAAiB,EACjB1D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAAsB,EACE;QACxB,OAAO,KAAK,CAAC6P,kBACX7N,KACA,IAAI,CAAC0M,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAuB;IAEJ;IAEA,MAAaJ,UACXtB,GAAsC,EACtCC,GAAsC,EACtCC,SAAkC,EAClCoR,UAAoB,EACL;QACf,OAAO,KAAK,CAAChQ,UACX,IAAI,CAAC8O,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBC,WACAoR;IAEJ;IAEUE,wBAAmD;QAC3D,IAAI,IAAI,CAACnR,WAAW,EAAE;YACpB,OAAO;QACT,OAAO;YACL,MAAMoR,WAA+B7Q,QAAQ,IAAI,CAACuI,sBAAsB;YACxE,OAAOsI;QACT;IACF;IAEA,yDAAyD,GACzD,MAAgBzL,gBAA4D;YAEvDyL;QADnB,MAAMA,WAAW,IAAI,CAACD,qBAAqB;QAC3C,MAAMzL,aAAa0L,6BAAAA,uBAAAA,SAAU1L,UAAU,qBAApB0L,oBAAsB,CAAC,IAAI;QAC9C,IAAI,CAAC1L,YAAY;YACf,MAAM2L,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEtD,IAAID,kBAAkB;oBAGhBA;gBAFJ,OAAO;oBACLrN,OAAOlJ,0BACLuW,EAAAA,2BAAAA,iBAAiBE,MAAM,qBAAvBF,yBAAyB7S,QAAQ,KAAI;wBACnC;4BAAEgT,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBAEHnN,MAAM;gBACR;YACF;YAEA;QACF;QAEA,OAAO;YACLN,OAAO9F,qBAAqBwH;YAC5BpB,MAAM;QACR;IACF;IAEUH,wBAAkC;QAC1C,MAAMiN,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO,EAAE;QACX;QAEA,OAAO3K,OAAO+C,IAAI,CAAC4H,SAASM,SAAS;IACvC;IAEA;;;;GAIC,GACD,AAAUC,oBAAoBlN,MAI7B,EAMQ;QACP,MAAM2M,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO;QACT;QAEA,IAAIQ;QAEJ,IAAI;YACFA,YAAYrX,oBAAoBC,kBAAkBiK,OAAOH,IAAI;QAC/D,EAAE,OAAOjB,KAAK;YACZ,OAAO;QACT;QAEA,IAAIwO,WAAWpN,OAAOiB,UAAU,GAC5B0L,SAAS1L,UAAU,CAACkM,UAAU,GAC9BR,SAASM,SAAS,CAACE,UAAU;QAEjC,IAAI,CAACC,UAAU;YACb,IAAI,CAACpN,OAAOiB,UAAU,EAAE;gBACtB,MAAM,IAAI/M,kBAAkBiZ;YAC9B;YACA,OAAO;QACT;QAEA,OAAO;YACLE,MAAMD,SAASC,IAAI;YACnBC,OAAOF,SAASG,KAAK,CAAC1J,GAAG,CAAC,CAAC2J,OAASnZ,KAAK,IAAI,CAAC2H,OAAO,EAAEwR;YACvDC,MAAM,AAACL,CAAAA,SAASK,IAAI,IAAI,EAAE,AAAD,EAAG5J,GAAG,CAAC,CAAC6J,UAAa,CAAA;oBAC5C,GAAGA,OAAO;oBACVC,UAAUtZ,KAAK,IAAI,CAAC2H,OAAO,EAAE0R,QAAQC,QAAQ;gBAC/C,CAAA;YACAC,QACER,SAASQ,MAAM,IACfR,SAASQ,MAAM,CAAC/J,GAAG,CAAC,CAAC6J;gBACnB,OAAO;oBACL,GAAGA,OAAO;oBACVC,UAAUtZ,KAAK,IAAI,CAAC2H,OAAO,EAAE0R,QAAQC,QAAQ;gBAC/C;YACF;YACF1U,KAAKmU,SAASnU,GAAG;QACnB;IACF;IAEA,MAAc4T,qBAAqB;QACjC,IAAI,CAAC7T,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC7B,IAAI,CAAC,IAAI,CAACsC,UAAU,CAACmH,YAAY,CAACkL,cAAc,EAAE;gBAChD;YACF;YAEA,IAAI;oBAOAC;gBANF,MAAMA,kBAAkB,IAAI,CAACjR,UAAU,CAACC,GAAG,GACvC,CAAC,IACDhB,QAAQzH,KAAK,IAAI,CAAC2H,OAAO,EAAE,UAAU5G;gBAEzC,IACE,IAAI,CAACyH,UAAU,CAACC,GAAG,KACnBgR,oCAAAA,6BAAAA,gBAAiBb,SAAS,qBAA1Ba,0BAA4B,CAAC,eAAe,GAC5C;oBACA,uDAAuD;oBACvD,OAAOhS,QAAQzH,KAAK,IAAI,CAAC2H,OAAO,EAAE,UAAU;gBAC9C;YACF,EAAE,OAAO4C,KAAK;gBACZ,IACE3I,QAAQ2I,QACRA,IAAI0D,IAAI,KAAK,YACb1D,IAAI0D,IAAI,KAAK,oBACb;oBACA,MAAM1D;gBACR;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,MAAgBmP,cAAc1S,QAAgB,EAAoB;QAChE,MAAM3B,OAAO,IAAI,CAACwT,mBAAmB,CAAC;YAAErN,MAAMxE;YAAU4F,YAAY;QAAK;QACzE,MAAM4M,iBAAiB,MAAM,IAAI,CAAChB,kBAAkB;QAEpD,IAAI,CAACnT,QAAQmU,gBAAgB;YAC3B,OAAO;QACT;QACA,OAAOlP,QAAQjF,QAAQA,KAAK4T,KAAK,CAACrD,MAAM,GAAG;IAC7C;IAEA;;;;GAIC,GACD,MAAgBvI,iBAAiB8I,IAAa,EAAE,CAAC;IACjD,MAAgBwD,mBAAmBC,OAIlC,EAAE,CAAC;IAEJ;;;;;GAKC,GACD,MAAgBrM,cAAc5B,MAM7B,EAAE;QACD,IAAIhH,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,0DAA0D;QAC1D,IACErD,0BAA0BqJ,OAAO6B,OAAO,EAAE,IAAI,CAAChF,UAAU,CAACqR,YAAY,EACnEC,oBAAoB,EACvB;YACA,OAAO;gBACLrM,UAAU,IAAIsM,SAAS,MAAM;oBAAElM,SAAS;wBAAE,qBAAqB;oBAAI;gBAAE;YACvE;QACF;QAEA,IAAIP;QAEJ,IAAI,IAAI,CAACnG,UAAU,CAAC6S,0BAA0B,EAAE;YAC9C1M,MAAMlN,eAAeuL,OAAO6B,OAAO,EAAE;QACvC,OAAO;YACL,mEAAmE;YACnE,MAAMjF,QAAQrG,uBAAuByJ,OAAOc,MAAM,CAAClE,KAAK,EAAE0R,QAAQ;YAClE,MAAMlE,SAAS3V,eAAeuL,OAAO6B,OAAO,EAAE;YAE9CF,MAAM,GAAGlN,eAAeuL,OAAO6B,OAAO,EAAE,gBAAgB,GAAG,EACzD,IAAI,CAAC0M,aAAa,IAAI,YACvB,CAAC,EAAE,IAAI,CAACC,IAAI,GAAGpE,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKpK,OAAOc,MAAM,CAACzF,QAAQ,GACjEuB,QAAQ,CAAC,CAAC,EAAEA,OAAO,GAAG,IACtB;QACJ;QAEA,IAAI,CAAC+E,IAAIrG,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAItB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM6F,OAGF,CAAC;QAEL,MAAMoB,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IAAI,CAACD,YAAY;YACf,OAAO;gBAAEsB,UAAU;YAAM;QAC3B;QACA,IAAI,CAAE,MAAM,IAAI,CAACwL,aAAa,CAAC9M,WAAWpB,IAAI,GAAI;YAChD,OAAO;gBAAE0C,UAAU;YAAM;QAC3B;QAEA,MAAM,IAAI,CAACb,gBAAgB,CAAC1B,OAAO6B,OAAO,CAACF,GAAG;QAC9C,MAAM8M,iBAAiB,IAAI,CAACvB,mBAAmB,CAAC;YAC9CrN,MAAMoB,WAAWpB,IAAI;YACrBoB,YAAY;QACd;QAEA,MAAMyN,SAAS,AAAC1O,CAAAA,OAAO6B,OAAO,CAAC6M,MAAM,IAAI,KAAI,EAAGC,WAAW;QAC3D,MAAMC,cAAc;YAClB1M,SAASlC,OAAO6B,OAAO,CAACK,OAAO;YAC/BwM;YACAlT,YAAY;gBACVqT,UAAU,IAAI,CAACrT,UAAU,CAACqT,QAAQ;gBAClCzP,MAAM,IAAI,CAAC5D,UAAU,CAAC4D,IAAI;gBAC1B0P,eAAe,IAAI,CAACtT,UAAU,CAACsT,aAAa;gBAC5CnM,cAAc,IAAI,CAACnH,UAAU,CAACmH,YAAY;YAC5C;YACAhB,KAAKA;YACL9B;YACAlE,MACE+S,WAAW,SAASA,WAAW,SAC1Bja,eAAeuL,OAAO6B,OAAO,EAAE,kBAChC3D;YAEN6Q,QAAQrX,uBAAuBsI,OAAO8B,QAAQ,CAACxD,gBAAgB;YAC/DqK,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,IAAIpH;QAIJ,qDAAqD;QACrD,8DAA8D;QAC9D,6DAA6D;QAC7D,6DAA6D;QAC7D,uBAAuB;QACvB,IAAI,CAACiN,gBAAgB;YACnB,IAAI7B;YACJA,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEhD,IAAI,CAACD,kBAAkB;gBACrB,MAAM,IAAIzY;YACZ;YACA,MAAM6a,YACJpC,iBAAiBtT,OAAO,IAAIsT;YAE9B,MAAMqC,iBACJ,CAAC;gBAAC;gBAAQ;aAAM,CAAC1C,QAAQ,CAACvM,OAAO6B,OAAO,CAAC6M,MAAM,KAC/C/P,QAAQiQ,YAAYjT,IAAI;YAE1B,IAAI;gBACF6F,SAAS,MAAMwN,UAAU;oBACvBlJ,SAAS8G,iBAAiB3L,UAAU,IAAI2L;oBACxC/K,SAAS;wBACP,GAAG+M,WAAW;wBACdjT,MAAMsT,iBACFL,YAAYjT,IAAI,CAACuT,eAAe,KAChChR;oBACN;oBACA2B,MAAM;gBACR;YACF,SAAU;gBACR,IAAIoP,gBAAgB;oBAClBL,YAAYjT,IAAI,CAACwT,QAAQ;gBAC3B;YACF;QACF,OAAO;YACL,MAAM,EAAEC,GAAG,EAAE,GAAGtT,QAAQ;YAExB0F,SAAS,MAAM4N,IAAI;gBACjBpT,SAAS,IAAI,CAACA,OAAO;gBACrBqR,MAAMoB,eAAepB,IAAI;gBACzBC,OAAOmB,eAAenB,KAAK;gBAC3B+B,mBAAmBZ;gBACnB5M,SAAS+M;gBACTU,UAAU;gBACVC,WAAWvP,OAAOuP,SAAS;YAC7B;QACF;QAEA,IAAI,CAAC,IAAI,CAAC1S,UAAU,CAACC,GAAG,EAAE;YACxB0E,OAAOmH,SAAS,CAACnF,KAAK,CAAC,CAAC7I;gBACtBD,QAAQC,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAEA;YAC1D;QACF;QAEA,IAAI,CAAC6G,QAAQ;YACX,IAAI,CAAChF,SAAS,CAACwD,OAAO6B,OAAO,EAAE7B,OAAO8B,QAAQ,EAAE9B,OAAOc,MAAM;YAC7D,OAAO;gBAAEyB,UAAU;YAAK;QAC1B;QAEA,sDAAsD;QACtD,IAAIf,OAAOM,QAAQ,CAACI,OAAO,CAACsN,GAAG,CAAC,eAAe;YAC7C,MAAMC,UAAUjO,OAAOM,QAAQ,CAACI,OAAO,CACpCwN,YAAY,GACZC,OAAO,CAAC,CAACC,sBACRzZ,mBAAmByZ;YAGvB,2BAA2B;YAC3BpO,OAAOM,QAAQ,CAACI,OAAO,CAAC2N,MAAM,CAAC;YAE/B,mCAAmC;YACnC,KAAK,MAAMC,UAAUL,QAAS;gBAC5BjO,OAAOM,QAAQ,CAACI,OAAO,CAAC6N,MAAM,CAAC,cAAcD;YAC/C;YAEA,+BAA+B;YAC/Btb,eAAewL,OAAO6B,OAAO,EAAE,oBAAoB4N;QACrD;QAEA,OAAOjO;IACT;IAmHUkF,uBAA0C;QAClD,IAAI,IAAI,CAACsJ,sBAAsB,EAAE;YAC/B,OAAO,IAAI,CAACA,sBAAsB;QACpC;QAEA,IAAI,CAACA,sBAAsB,GAAGrY,aAC5BtD,KAAK,IAAI,CAAC2H,OAAO,EAAEnH;QAGrB,OAAO,IAAI,CAACmb,sBAAsB;IACpC;IAEUpM,oBAAyD;QACjE,OAAOzM,YAAY2R,KAAK,CACtB1R,mBAAmBwM,iBAAiB,EACpC,IAAMjM,aAAatD,KAAK,IAAI,CAAC2H,OAAO,EAAElH;IAE1C;IAEUmb,kBACR/U,GAAoB,EACpBE,SAAiC,EACjC8U,YAAsB,EACtB;YAEiBhV;QADjB,6BAA6B;QAC7B,MAAMiV,WAAWjV,EAAAA,+BAAAA,IAAIgH,OAAO,CAAC,oBAAoB,qBAAhChH,6BAAkCqR,QAAQ,CAAC,YACxD,UACA;QAEJ,4DAA4D;QAC5D,MAAMpL,UACJ,IAAI,CAACoN,aAAa,IAAI,IAAI,CAACC,IAAI,GAC3B,GAAG2B,SAAS,GAAG,EAAE,IAAI,CAAC5B,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,GAAGtT,IAAIyG,GAAG,EAAE,GAC5D,IAAI,CAACnG,UAAU,CAACmH,YAAY,CAACyN,eAAe,GAC1C,CAAC,QAAQ,EAAElV,IAAIgH,OAAO,CAACmO,IAAI,IAAI,cAAcnV,IAAIyG,GAAG,EAAE,GACtDzG,IAAIyG,GAAG;QAEfnN,eAAe0G,KAAK,WAAWiG;QAC/B3M,eAAe0G,KAAK,aAAa;YAAE,GAAGE,UAAUwB,KAAK;QAAC;QACtDpI,eAAe0G,KAAK,gBAAgBiV;QAEpC,IAAI,CAACD,cAAc;YACjB1b,eAAe0G,KAAK,gBAAgBxE,iBAAiBwE,IAAIyB,eAAe;QAC1E;IACF;IAEA,MAAgBoD,gBAAgBC,MAU/B,EAAoC;QACnC,IAAIhH,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIc,MACR,wGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIsW;QAEJ,MAAM,EAAE1T,KAAK,EAAEiD,IAAI,EAAEN,KAAK,EAAE,GAAGS;QAE/B,IAAI,CAACT,OACH,MAAM,IAAI,CAACyO,kBAAkB,CAAC;YAC5BnO;YACAI,UAAUD,OAAOC,QAAQ;YACzB0B,KAAK3B,OAAO9E,GAAG,CAACyG,GAAG;QACrB;QACF2O,WAAW,IAAI,CAACpD,mBAAmB,CAAC;YAClCrN;YACAoB,YAAY;QACd;QAEA,IAAI,CAACqP,UAAU;YACb,OAAO;QACT;QAEA,6DAA6D;QAC7D,MAAMC,oBAAoB9b,eAAeuL,OAAO9E,GAAG,EAAE;QACrD,MAAMsV,aAAa,IAAIC,IACrBhc,eAAeuL,OAAO9E,GAAG,EAAE,cAAc,KACzC;QAEF,MAAMwV,cAAcna,uBAAuB;YACzC,GAAGyL,OAAO2O,WAAW,CAACH,WAAWI,YAAY,CAAC;YAC9C,GAAGhU,KAAK;YACR,GAAGoD,OAAOA,MAAM;QAClB,GAAGsO,QAAQ;QAEX,IAAIiC,mBAAmB;YACrBvQ,OAAO9E,GAAG,CAACgH,OAAO,CAAC,gBAAgB,GAAG;QACxC;QACAsO,WAAWjI,MAAM,GAAGmI;QACpB,MAAM/O,MAAM6O,WAAWlC,QAAQ;QAE/B,IAAI,CAAC3M,IAAIrG,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAItB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEoV,GAAG,EAAE,GAAGtT,QAAQ;QACxB,MAAM0F,SAAS,MAAM4N,IAAI;YACvBpT,SAAS,IAAI,CAACA,OAAO;YACrBqR,MAAMiD,SAASjD,IAAI;YACnBC,OAAOgD,SAAShD,KAAK;YACrB+B,mBAAmBiB;YACnBzO,SAAS;gBACPK,SAASlC,OAAO9E,GAAG,CAACgH,OAAO;gBAC3BwM,QAAQ1O,OAAO9E,GAAG,CAACwT,MAAM;gBACzBlT,YAAY;oBACVqT,UAAU,IAAI,CAACrT,UAAU,CAACqT,QAAQ;oBAClCzP,MAAM,IAAI,CAAC5D,UAAU,CAAC4D,IAAI;oBAC1B0P,eAAe,IAAI,CAACtT,UAAU,CAACsT,aAAa;gBAC9C;gBACAnN;gBACA9B,MAAM;oBACJwN,MAAMrN,OAAOH,IAAI;oBACjB,GAAIG,OAAOA,MAAM,IAAI;wBAAEA,QAAQA,OAAOA,MAAM;oBAAC,CAAC;gBAChD;gBACArE,MAAMlH,eAAeuL,OAAO9E,GAAG,EAAE;gBACjC6T,QAAQrX,uBAAuBsI,OAAO7E,GAAG,CAACmD,gBAAgB;gBAC1DqK,WAAW,IAAI,CAACC,YAAY;YAC9B;YACA0G,UAAU;YACVuB,SAAS7Q,OAAO6Q,OAAO;YACvBtB,WAAWvP,OAAOuP,SAAS;YAC3BnR,kBACE,AAAC0S,WAAmBC,kBAAkB,IACtCtc,eAAeuL,OAAO9E,GAAG,EAAE;YAC7B8V,0BAA0Bvc,eACxBuL,OAAO9E,GAAG,EACV;QAEJ;QAEA,IAAIsG,OAAOyP,YAAY,EAAE;YACvBjR,OAAO9E,GAAG,CAAC+V,YAAY,GAAGzP,OAAOyP,YAAY;QAC/C;QAEA,IAAI,CAACjR,OAAO7E,GAAG,CAACO,UAAU,IAAIsE,OAAO7E,GAAG,CAACO,UAAU,GAAG,KAAK;YACzDsE,OAAO7E,GAAG,CAACO,UAAU,GAAG8F,OAAOM,QAAQ,CAACM,MAAM;YAC9CpC,OAAO7E,GAAG,CAAC+V,aAAa,GAAG1P,OAAOM,QAAQ,CAACqP,UAAU;QACvD;QAEA,8CAA8C;QAE9C3P,OAAOM,QAAQ,CAACI,OAAO,CAACkP,OAAO,CAAC,CAACzT,OAAOoE;YACtC,yDAAyD;YACzD,IAAIA,IAAIsP,WAAW,OAAO,cAAc;gBACtC,qFAAqF;gBACrF,KAAK,MAAMvB,UAAU3Z,mBAAmBwH,OAAQ;oBAC9CqC,OAAO7E,GAAG,CAACmW,YAAY,CAACvP,KAAK+N;gBAC/B;YACF,OAAO;gBACL9P,OAAO7E,GAAG,CAACmW,YAAY,CAACvP,KAAKpE;YAC/B;QACF;QAEA,MAAM,EAAEW,gBAAgB,EAAE,GAAG0B,OAAO7E,GAAG;QACvC,IAAIqG,OAAOM,QAAQ,CAACnG,IAAI,EAAE;YACxB,MAAMpE,mBAAmBiK,OAAOM,QAAQ,CAACnG,IAAI,EAAE2C;QACjD,OAAO;YACLA,iBAAiB+D,GAAG;QACtB;QAEA,OAAOb;IACT;IAEA,IAAc8C,gBAAwB;QACpC,IAAI,IAAI,CAACiN,cAAc,EAAE;YACvB,OAAO,IAAI,CAACA,cAAc;QAC5B;QACA,MAAMjN,gBAAgBjQ,KAAK,IAAI,CAAC2H,OAAO,EAAE/G;QACzC,IAAI,CAACsc,cAAc,GAAGjN;QACtB,OAAOA;IACT;IAEA,MAAgBkN,2BACdhH,IAAa,EAC6B;QAC1C,uEAAuE;QACvE,sBAAsB;QACtB,OAAO;IACT;IAEA,MAAgBrK,8BACd,GAAGsR,IAAqD,EACxD;QACA,MAAM,KAAK,CAACtR,iCAAiCsR;QAE7C,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAAC5U,UAAU,CAACC,GAAG,EAAE;YACxB,IAAI,CAAC6D,QAAQ,CAAC8Q,IAAI,CAAC,EAAE;QACvB;IACF;IAEUC,cAAcC,QAA6B,EAAE;QACrD,IAAI,CAAC3W,gBAAgB,CAAC4W,GAAG,CAACD;IAC5B;IAEA,MAAME,QAAuB;QAC3B,MAAM,IAAI,CAAC7W,gBAAgB,CAAC8W,MAAM;IACpC;IAEUC,uBAAkC;QAC1C,IAAI,CAACC,iBAAiB,KAAK,IAAI,CAACC,uBAAuB;QACvD,OAAO,IAAI,CAACD,iBAAiB;IAC/B;IAEQC,0BAA0B;QAChC,IAAI,IAAI,CAAC1W,WAAW,EAAE;YACpB,MAAM,qBAEL,CAFK,IAAIrD,eACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMga,UAAU,IAAI/Z,YAAY;YAAE0Y,SAASnW,QAAQC,KAAK;QAAC;QAEzD,kEAAkE;QAClE,IAAI,CAAC+W,aAAa,CAAC,IAAMQ,QAAQC,QAAQ;QAEzC,OAAOD,QAAQvJ,SAAS;IAC1B;AACF", "ignoreList": [0]}